<?php $__env->startSection('title', ($product->meta_title ?: $product->name) . ' - ' . __('common.company_name')); ?>
<?php $__env->startSection('meta_description', $product->meta_description ?: $product->short_description ?: Str::limit(strip_tags($product->description), 160)); ?>
<?php $__env->startSection('meta_keywords', $product->meta_keywords ?: $product->categories->pluck('name')->implode(', ')); ?>

<?php $__env->startSection('og_title', ($product->meta_title ?: $product->name) . ' - ' . __('common.company_name')); ?>
<?php $__env->startSection('og_description', $product->meta_description ?: $product->short_description ?: Str::limit(strip_tags($product->description), 160)); ?>
<?php $__env->startSection('og_image', $product->featured_image ? asset('storage/' . $product->featured_image) : asset('images/og-image.jpg')); ?>

<?php $__env->startSection('twitter_title', ($product->meta_title ?: $product->name) . ' - ' . __('common.company_name')); ?>
<?php $__env->startSection('twitter_description', $product->meta_description ?: $product->short_description ?: Str::limit(strip_tags($product->description), 160)); ?>
<?php $__env->startSection('twitter_image', $product->featured_image ? asset('storage/' . $product->featured_image) : asset('images/twitter-image.jpg')); ?>

<?php $__env->startSection('content'); ?>
<!-- Product Detail -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <!-- Product Images -->
            <div>
                <div class="bg-white rounded-lg shadow-sm p-8">
                    <!-- Main Image -->
                    <div class="mb-4">
                        <img id="main-image" src="<?php echo e($product->featured_image ? asset('storage/' . $product->featured_image) : $product->primary_image); ?>"
                             alt="<?php echo e($product->name); ?>"
                             class="w-full h-96 object-cover rounded-lg">
                    </div>

                    <!-- Image Gallery -->
                    <?php if($product->gallery && count($product->gallery) > 0): ?>
                        <div class="grid grid-cols-4 gap-2">
                            <!-- Featured Image Thumbnail -->
                            <?php if($product->featured_image): ?>
                                <button type="button"
                                        class="gallery-thumb border-2 border-blue-500 rounded-lg overflow-hidden"
                                        onclick="changeMainImage('<?php echo e(asset('storage/' . $product->featured_image)); ?>', this)">
                                    <img src="<?php echo e(asset('storage/' . $product->featured_image)); ?>"
                                         alt="<?php echo e($product->name); ?>"
                                         class="w-full h-20 object-cover">
                                </button>
                            <?php endif; ?>

                            <!-- Gallery Images Thumbnails -->
                            <?php $__currentLoopData = $product->gallery; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <button type="button"
                                        class="gallery-thumb border-2 border-gray-200 hover:border-blue-500 rounded-lg overflow-hidden transition-colors"
                                        onclick="changeMainImage('<?php echo e(asset('storage/' . $image)); ?>', this)">
                                    <img src="<?php echo e(asset('storage/' . $image)); ?>"
                                         alt="<?php echo e($product->name); ?>"
                                         class="w-full h-20 object-cover">
                                </button>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Product Features -->
                <?php if($product->is_featured): ?>
                    <div class="mt-4 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-yellow-500 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"/>
                            </svg>
                            <span class="text-yellow-800 font-medium">Featured Product</span>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
            
            <!-- Product Info -->
            <div>
                <!-- Breadcrumb -->
                <nav class="mb-4">
                    <ol class="flex items-center space-x-2 text-sm text-gray-500">
                        <li><a href="<?php echo e(route('shop.index')); ?>" class="hover:text-blue-600">Shop</a></li>
                        <?php if($product->categories->first()): ?>
                            <li><span class="mx-2">/</span></li>
                            <li><a href="<?php echo e(route('shop.category', $product->categories->first()->slug)); ?>" class="hover:text-blue-600"><?php echo e($product->categories->first()->name); ?></a></li>
                        <?php endif; ?>
                        <li><span class="mx-2">/</span></li>
                        <li class="text-gray-900"><?php echo e($product->name); ?></li>
                    </ol>
                </nav>

                <!-- Categories -->
                <div class="mb-4">
                    <?php $__currentLoopData = $product->categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <a href="<?php echo e(route('shop.category', $category->slug)); ?>"
                       class="inline-block px-3 py-1 bg-blue-100 text-blue-600 text-sm rounded-full mr-2 hover:bg-blue-200 transition-colors">
                        <?php echo e($category->name); ?>

                    </a>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <!-- Product Title -->
                <h1 class="heading-1 text-gray-900 mb-4"><?php echo e($product->name); ?></h1>

                <!-- Product Meta Info -->
                <div class="flex items-center space-x-4 mb-6 text-sm text-gray-600">
                    <?php if($product->sku): ?>
                        <span><strong>SKU:</strong> <?php echo e($product->sku); ?></span>
                    <?php endif; ?>
                    <?php if($product->brand): ?>
                        <span><strong>Brand:</strong> <?php echo e($product->brand); ?></span>
                    <?php endif; ?>
                    <?php if($product->model_number): ?>
                        <span><strong>Model:</strong> <?php echo e($product->model_number); ?></span>
                    <?php endif; ?>
                </div>
                
                <div class="flex items-center space-x-4 mb-6">
                    <span class="text-3xl font-bold text-gray-900"><?php echo e($product->formatted_price); ?></span>
                    <?php if($product->compare_price): ?>
                    <span class="text-xl text-gray-500 line-through"><?php echo e($product->formatted_compare_price); ?></span>
                    <span class="px-2 py-1 bg-red-100 text-red-600 text-sm rounded">
                        Save <?php echo e($product->discount_percentage); ?>%
                    </span>
                    <?php endif; ?>
                </div>
                
                <?php if($product->short_description): ?>
                <div class="text-lead text-gray-600 mb-6 prose prose-gray max-w-none">
                    <?php echo $product->short_description; ?>

                </div>
                <?php endif; ?>

                <div class="mb-8">
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">Description</h3>
                    <div class="prose prose-gray max-w-none">
                        <?php echo $product->description; ?>

                    </div>
                </div>
                
                <!-- Add to Cart Form -->
                <form id="add-to-cart-form" class="space-y-6">
                    <?php echo csrf_field(); ?>
                    <input type="hidden" name="product_id" value="<?php echo e($product->id); ?>">
                    
                    <?php if($product->variants->count() > 0): ?>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Select Option</label>
                        <select name="variant_id" class="form-input">
                            <?php $__currentLoopData = $product->variants; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $variant): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($variant->id); ?>" <?php echo e(!$variant->isInStock() ? 'disabled' : ''); ?>>
                                <?php echo e($variant->name); ?> - <?php echo e($variant->formatted_price); ?>

                                <?php if(!$variant->isInStock()): ?> (Out of Stock) <?php endif; ?>
                            </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    <?php endif; ?>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Quantity</label>
                        <div class="flex items-center space-x-3">
                            <button type="button" class="quantity-btn decrease-qty px-3 py-2 border border-gray-300 rounded-l-lg hover:bg-gray-50">-</button>
                            <input type="number" name="quantity" value="1" min="1" max="10" 
                                   class="quantity-input w-20 px-3 py-2 border-t border-b border-gray-300 text-center focus:ring-0 focus:border-gray-300">
                            <button type="button" class="quantity-btn increase-qty px-3 py-2 border border-gray-300 rounded-r-lg hover:bg-gray-50">+</button>
                        </div>
                    </div>
                    
                    <div class="space-y-3">
                        <?php if($product->isInStock()): ?>
                        <button type="submit" class="w-full btn-primary">
                            Add to Cart
                            <svg class="ml-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"></path>
                            </svg>
                        </button>
                        <?php else: ?>
                        <div class="w-full bg-gray-300 text-gray-500 px-6 py-3 rounded-lg text-center">
                            Out of Stock
                        </div>
                        <?php endif; ?>
                        
                        <div class="text-center">
                            <span class="text-sm text-gray-500">
                                Stock Status: <span class="font-medium"><?php echo e($product->stock_status); ?></span>
                            </span>
                        </div>
                    </div>
                </form>
                
                <!-- Product Details -->
                <div class="mt-8 pt-8 border-t border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Product Details</h3>
                    <dl class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <?php if($product->sku): ?>
                            <div class="flex flex-col">
                                <dt class="text-sm font-medium text-gray-500 mb-1">SKU</dt>
                                <dd class="text-sm text-gray-900 font-mono bg-gray-50 px-2 py-1 rounded"><?php echo e($product->sku); ?></dd>
                            </div>
                        <?php endif; ?>

                        <?php if($product->brand): ?>
                            <div class="flex flex-col">
                                <dt class="text-sm font-medium text-gray-500 mb-1">Brand</dt>
                                <dd class="text-sm text-gray-900"><?php echo e($product->brand); ?></dd>
                            </div>
                        <?php endif; ?>

                        <?php if($product->model_number): ?>
                            <div class="flex flex-col">
                                <dt class="text-sm font-medium text-gray-500 mb-1">Model</dt>
                                <dd class="text-sm text-gray-900"><?php echo e($product->model_number); ?></dd>
                            </div>
                        <?php endif; ?>

                        <?php if($product->barcode): ?>
                            <div class="flex flex-col">
                                <dt class="text-sm font-medium text-gray-500 mb-1">Barcode</dt>
                                <dd class="text-sm text-gray-900 font-mono"><?php echo e($product->barcode); ?></dd>
                            </div>
                        <?php endif; ?>

                        <?php if($product->weight): ?>
                            <div class="flex flex-col">
                                <dt class="text-sm font-medium text-gray-500 mb-1">Weight</dt>
                                <dd class="text-sm text-gray-900"><?php echo e($product->weight); ?>g</dd>
                            </div>
                        <?php endif; ?>

                        <?php if($product->dimensions && ($product->dimensions['length'] || $product->dimensions['width'] || $product->dimensions['height'])): ?>
                            <div class="flex flex-col">
                                <dt class="text-sm font-medium text-gray-500 mb-1">Dimensions (L×W×H)</dt>
                                <dd class="text-sm text-gray-900">
                                    <?php echo e($product->dimensions['length'] ?? '0'); ?> ×
                                    <?php echo e($product->dimensions['width'] ?? '0'); ?> ×
                                    <?php echo e($product->dimensions['height'] ?? '0'); ?> cm
                                </dd>
                            </div>
                        <?php endif; ?>

                        <?php if($product->track_inventory): ?>
                            <div class="flex flex-col">
                                <dt class="text-sm font-medium text-gray-500 mb-1">Stock Quantity</dt>
                                <dd class="text-sm text-gray-900">
                                    <?php echo e($product->inventory_quantity); ?> units
                                    <?php if($product->inventory_quantity <= $product->low_stock_threshold): ?>
                                        <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            Low Stock
                                        </span>
                                    <?php endif; ?>
                                </dd>
                            </div>
                        <?php endif; ?>

                        <div class="flex flex-col md:col-span-2">
                            <dt class="text-sm font-medium text-gray-500 mb-1">Categories</dt>
                            <dd class="text-sm text-gray-900">
                                <?php $__currentLoopData = $product->categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <a href="<?php echo e(route('shop.category', $category->slug)); ?>"
                                       class="inline-block px-2 py-1 bg-blue-100 text-blue-600 text-xs rounded mr-1 mb-1 hover:bg-blue-200 transition-colors">
                                        <?php echo e($category->name); ?>

                                    </a>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </dd>
                        </div>
                    </dl>
                </div>
            </div>
        </div>
        
        <!-- Related Products -->
        <?php if($relatedProducts->count() > 0): ?>
        <div class="mt-20">
            <h2 class="heading-2 text-center mb-12">
                Related <span class="text-blue-600">Products</span>
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <?php $__currentLoopData = $relatedProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $relatedProduct): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow">
                    <div class="relative">
                        <a href="<?php echo e(route('shop.product', $relatedProduct->slug)); ?>">
                            <img src="<?php echo e($relatedProduct->primary_image); ?>" alt="<?php echo e($relatedProduct->name); ?>" 
                                 class="w-full h-48 object-cover">
                        </a>
                        
                        <?php if($relatedProduct->discount_percentage > 0): ?>
                        <div class="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-xs font-medium">
                            -<?php echo e($relatedProduct->discount_percentage); ?>%
                        </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="p-4">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">
                            <a href="<?php echo e(route('shop.product', $relatedProduct->slug)); ?>" class="hover:text-blue-600 transition-colors">
                                <?php echo e($relatedProduct->name); ?>

                            </a>
                        </h3>
                        
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <span class="text-lg font-bold text-gray-900"><?php echo e($relatedProduct->formatted_price); ?></span>
                                <?php if($relatedProduct->compare_price): ?>
                                <span class="text-sm text-gray-500 line-through"><?php echo e($relatedProduct->formatted_compare_price); ?></span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
        <?php endif; ?>
    </div>
</section>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Quantity controls
    const quantityInput = document.querySelector('.quantity-input');
    const decreaseBtn = document.querySelector('.decrease-qty');
    const increaseBtn = document.querySelector('.increase-qty');
    
    decreaseBtn.addEventListener('click', function() {
        const currentValue = parseInt(quantityInput.value);
        if (currentValue > 1) {
            quantityInput.value = currentValue - 1;
        }
    });
    
    increaseBtn.addEventListener('click', function() {
        const currentValue = parseInt(quantityInput.value);
        if (currentValue < 10) {
            quantityInput.value = currentValue + 1;
        }
    });
    
    // Add to cart form
    const addToCartForm = document.getElementById('add-to-cart-form');
    if (addToCartForm) {
        addToCartForm.addEventListener('submit', function(e) {
            e.preventDefault();

            console.log('🛒 Add to cart form submitted');

            const formData = new FormData(this);
            const submitButton = this.querySelector('button[type="submit"]');
            const originalText = submitButton.innerHTML;

            // Log form data
            console.log('📝 Form data:', Object.fromEntries(formData));

            submitButton.innerHTML = `
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Adding...
            `;
            submitButton.disabled = true;

            console.log('🚀 Sending request to /cart/add');

            fetch('/cart/add', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: formData
            })
            .then(response => {
                console.log('📡 Response received:', response.status, response.statusText);
                return response.json();
            })
            .then(data => {
                console.log('📦 Response data:', data);
                if (data.success) {
                    showNotification(data.message, 'success');

                    // Update cart count in header
                    updateCartCount(data.cart_count || data.cart.item_count);

                    // Change button to "View Cart"
                    submitButton.innerHTML = `
                        <svg class="mr-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"></path>
                        </svg>
                        View Cart
                    `;
                    submitButton.disabled = false;

                    // Change button behavior to redirect to cart
                    submitButton.onclick = function(e) {
                        e.preventDefault();
                        window.location.href = '/cart';
                    };

                    // Remove form submit listener to prevent double submission
                    addToCartForm.removeEventListener('submit', arguments.callee);
                } else {
                    console.error('❌ Cart add failed:', data.message);
                    showNotification(data.message, 'error');
                    submitButton.innerHTML = originalText;
                    submitButton.disabled = false;
                }
            })
            .catch(error => {
                console.error('💥 Cart add error:', error);
                showNotification('An error occurred. Please try again.', 'error');
                submitButton.innerHTML = originalText;
                submitButton.disabled = false;
            });
        });
    }
});

// Note: updateCartCount and showNotification functions are now loaded globally from cart-utils.js
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('structured_data'); ?>
<script type="application/ld+json">
{
    "@context": "https://schema.org/",
    "@type": "Product",
    "name": "<?php echo e($product->name); ?>",
    "description": "<?php echo e($product->meta_description ?: $product->short_description ?: strip_tags($product->description)); ?>",
    "image": [
        <?php if($product->featured_image): ?>
            "<?php echo e(asset('storage/' . $product->featured_image)); ?>"
            <?php if($product->gallery && count($product->gallery) > 0): ?>,<?php endif; ?>
        <?php endif; ?>
        <?php if($product->gallery && count($product->gallery) > 0): ?>
            <?php $__currentLoopData = $product->gallery; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                "<?php echo e(asset('storage/' . $image)); ?>"<?php if(!$loop->last): ?>,<?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php endif; ?>
    ],
    <?php if($product->sku): ?>
    "sku": "<?php echo e($product->sku); ?>",
    <?php endif; ?>
    <?php if($product->brand): ?>
    "brand": {
        "@type": "Brand",
        "name": "<?php echo e($product->brand); ?>"
    },
    <?php endif; ?>
    <?php if($product->model_number): ?>
    "model": "<?php echo e($product->model_number); ?>",
    <?php endif; ?>
    "offers": {
        "@type": "Offer",
        "url": "<?php echo e(route('shop.product', $product->slug)); ?>",
        "priceCurrency": "ZAR",
        "price": "<?php echo e($product->price); ?>",
        <?php if($product->compare_price): ?>
        "priceValidUntil": "<?php echo e(now()->addYear()->format('Y-m-d')); ?>",
        <?php endif; ?>
        "availability": "<?php echo e($product->isInStock() ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock'); ?>",
        "seller": {
            "@type": "Organization",
            "name": "<?php echo e(__('common.company_name')); ?>"
        }
    },
    "category": "<?php echo e($product->categories->first()->name ?? 'Products'); ?>",
    <?php if($product->weight): ?>
    "weight": {
        "@type": "QuantitativeValue",
        "value": "<?php echo e($product->weight); ?>",
        "unitCode": "GRM"
    },
    <?php endif; ?>
    "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "5",
        "reviewCount": "1"
    }
}
</script>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Image gallery functionality
function changeMainImage(imageSrc, thumbElement) {
    document.getElementById('main-image').src = imageSrc;

    // Update thumbnail borders
    document.querySelectorAll('.gallery-thumb').forEach(thumb => {
        thumb.classList.remove('border-blue-500');
        thumb.classList.add('border-gray-200');
    });

    thumbElement.classList.remove('border-gray-200');
    thumbElement.classList.add('border-blue-500');
}

// Quantity controls


// Notification function
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    const bgColor = {
        'success': 'bg-green-500',
        'error': 'bg-red-500',
        'warning': 'bg-yellow-500',
        'info': 'bg-blue-500'
    }[type] || 'bg-blue-500';

    notification.className = `fixed top-4 right-4 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-transform duration-300 translate-x-full`;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Slide in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // Slide out and remove
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\files\chisolution\resources\views/pages/shop/product.blade.php ENDPATH**/ ?>