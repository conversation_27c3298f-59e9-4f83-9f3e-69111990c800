<?php $__env->startSection('title', 'My Projects - ' . __('common.company_name')); ?>
<?php $__env->startSection('page_title', 'My Projects'); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">My Projects</h1>
                <p class="text-gray-600">Track and manage your project portfolio</p>
            </div>
            <a href="<?php echo e(route('contact')); ?>" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200 text-center">
                <svg class="w-5 h-5 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Start New Project
            </a>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <form method="GET" action="<?php echo e(route('my-projects.index')); ?>" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- Search -->
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-2">Search Projects</label>
                    <input type="text"
                           id="search"
                           name="search"
                           value="<?php echo e(request('search')); ?>"
                           placeholder="Search by title, description, or client name..."
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <!-- Status Filter -->
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select id="status"
                            name="status"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="all" <?php echo e(request('status') === 'all' || !request('status') ? 'selected' : ''); ?>>All Statuses</option>
                        <option value="planning" <?php echo e(request('status') === 'planning' ? 'selected' : ''); ?>>Planning</option>
                        <option value="in_progress" <?php echo e(request('status') === 'in_progress' ? 'selected' : ''); ?>>In Progress</option>
                        <option value="review" <?php echo e(request('status') === 'review' ? 'selected' : ''); ?>>Review</option>
                        <option value="completed" <?php echo e(request('status') === 'completed' ? 'selected' : ''); ?>>Completed</option>
                        <option value="on_hold" <?php echo e(request('status') === 'on_hold' ? 'selected' : ''); ?>>On Hold</option>
                        <option value="cancelled" <?php echo e(request('status') === 'cancelled' ? 'selected' : ''); ?>>Cancelled</option>
                    </select>
                </div>

                <!-- Priority Filter -->
                <div>
                    <label for="priority" class="block text-sm font-medium text-gray-700 mb-2">Priority</label>
                    <select id="priority"
                            name="priority"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="all" <?php echo e(request('priority') === 'all' || !request('priority') ? 'selected' : ''); ?>>All Priorities</option>
                        <option value="low" <?php echo e(request('priority') === 'low' ? 'selected' : ''); ?>>Low</option>
                        <option value="medium" <?php echo e(request('priority') === 'medium' ? 'selected' : ''); ?>>Medium</option>
                        <option value="high" <?php echo e(request('priority') === 'high' ? 'selected' : ''); ?>>High</option>
                        <option value="urgent" <?php echo e(request('priority') === 'urgent' ? 'selected' : ''); ?>>Urgent</option>
                    </select>
                </div>
            </div>

            <div class="flex flex-col sm:flex-row gap-3">
                <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200">
                    <svg class="w-4 h-4 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    Search
                </button>
                <a href="<?php echo e(route('my-projects.index')); ?>" class="bg-gray-100 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-200 transition-colors duration-200 text-center">
                    Clear Filters
                </a>
            </div>
        </form>
    </div>

    <!-- Status Tabs -->
    <div class="mb-6">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8 overflow-x-auto">
                <a href="<?php echo e(route('my-projects.index', array_merge(request()->except('status'), ['status' => 'all']))); ?>"
                   class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200
                          <?php echo e((!request('status') || request('status') === 'all') ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'); ?>">
                    All Projects
                    <span class="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs font-medium"><?php echo e($statusCounts['all']); ?></span>
                </a>
                <a href="<?php echo e(route('my-projects.index', array_merge(request()->except('status'), ['status' => 'planning']))); ?>"
                   class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200
                          <?php echo e(request('status') === 'planning' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'); ?>">
                    Planning
                    <span class="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs font-medium"><?php echo e($statusCounts['planning']); ?></span>
                </a>
                <a href="<?php echo e(route('my-projects.index', array_merge(request()->except('status'), ['status' => 'in_progress']))); ?>"
                   class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200
                          <?php echo e(request('status') === 'in_progress' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'); ?>">
                    In Progress
                    <span class="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs font-medium"><?php echo e($statusCounts['in_progress']); ?></span>
                </a>
                <a href="<?php echo e(route('my-projects.index', array_merge(request()->except('status'), ['status' => 'review']))); ?>"
                   class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200
                          <?php echo e(request('status') === 'review' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'); ?>">
                    Review
                    <span class="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs font-medium"><?php echo e($statusCounts['review']); ?></span>
                </a>
                <a href="<?php echo e(route('my-projects.index', array_merge(request()->except('status'), ['status' => 'completed']))); ?>"
                   class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200
                          <?php echo e(request('status') === 'completed' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'); ?>">
                    Completed
                    <span class="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs font-medium"><?php echo e($statusCounts['completed']); ?></span>
                </a>
                <a href="<?php echo e(route('my-projects.index', array_merge(request()->except('status'), ['status' => 'on_hold']))); ?>"
                   class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200
                          <?php echo e(request('status') === 'on_hold' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'); ?>">
                    On Hold
                    <span class="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs font-medium"><?php echo e($statusCounts['on_hold']); ?></span>
                </a>
                <a href="<?php echo e(route('my-projects.index', array_merge(request()->except('status'), ['status' => 'cancelled']))); ?>"
                   class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200
                          <?php echo e(request('status') === 'cancelled' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'); ?>">
                    Cancelled
                    <span class="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs font-medium"><?php echo e($statusCounts['cancelled']); ?></span>
                </a>
            </nav>
        </div>
    </div>

    <?php if($projects->count() > 0): ?>
        <!-- Projects Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <?php $__currentLoopData = $projects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-all duration-200">
                    <?php if($project->featured_image): ?>
                        <div class="aspect-w-16 aspect-h-9">
                            <img src="<?php echo e($project->featured_image_url); ?>" alt="<?php echo e($project->title); ?>" class="w-full h-48 object-cover rounded-t-lg">
                        </div>
                    <?php endif; ?>

                    <div class="p-6">
                        <!-- Status and Priority Badges -->
                        <div class="flex items-center justify-between mb-4">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                <?php if($project->status === 'completed'): ?> bg-green-100 text-green-800
                                <?php elseif($project->status === 'in_progress'): ?> bg-blue-100 text-blue-800
                                <?php elseif($project->status === 'review'): ?> bg-purple-100 text-purple-800
                                <?php elseif($project->status === 'planning'): ?> bg-gray-100 text-gray-800
                                <?php elseif($project->status === 'on_hold'): ?> bg-yellow-100 text-yellow-800
                                <?php elseif($project->status === 'cancelled'): ?> bg-red-100 text-red-800
                                <?php else: ?> bg-gray-100 text-gray-800
                                <?php endif; ?>">
                                <?php echo e(ucfirst(str_replace('_', ' ', $project->status))); ?>

                            </span>
                            <?php if($project->priority): ?>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                    <?php if($project->priority === 'urgent'): ?> bg-red-100 text-red-800
                                    <?php elseif($project->priority === 'high'): ?> bg-orange-100 text-orange-800
                                    <?php elseif($project->priority === 'medium'): ?> bg-yellow-100 text-yellow-800
                                    <?php elseif($project->priority === 'low'): ?> bg-green-100 text-green-800
                                    <?php else: ?> bg-gray-100 text-gray-800
                                    <?php endif; ?>">
                                    <?php echo e(ucfirst($project->priority)); ?>

                                </span>
                            <?php endif; ?>
                        </div>

                        <!-- Project Title and Description -->
                        <h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-2"><?php echo e($project->title); ?></h3>
                        <p class="text-gray-600 text-sm mb-4 line-clamp-3"><?php echo e($project->description); ?></p>

                        <!-- Service Type -->
                        <?php if($project->service): ?>
                            <div class="mb-3">
                                <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-700">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                    </svg>
                                    <?php echo e($project->service->title); ?>

                                </span>
                            </div>
                        <?php endif; ?>

                        <!-- Project Value -->
                        <?php if($project->total_amount): ?>
                            <div class="mb-4">
                                <p class="text-lg font-semibold text-gray-900"><?php echo e($project->formatted_total); ?></p>
                                <?php if($project->hourly_rate): ?>
                                    <p class="text-sm text-gray-500">R<?php echo e(number_format($project->hourly_rate, 2)); ?>/hour</p>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>

                        <!-- Progress Bar -->
                        <?php if($project->estimated_hours && $project->actual_hours): ?>
                            <div class="mb-4">
                                <div class="flex justify-between text-sm text-gray-600 mb-1">
                                    <span>Progress</span>
                                    <span><?php echo e(number_format($project->actual_hours, 1)); ?>/<?php echo e(number_format($project->estimated_hours, 1)); ?> hours</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: <?php echo e($project->progress_percentage); ?>%"></div>
                                </div>
                                <div class="text-xs text-gray-500 mt-1"><?php echo e($project->progress_percentage); ?>% complete</div>
                            </div>
                        <?php endif; ?>

                        <!-- Project Dates -->
                        <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                            <?php if($project->start_date): ?>
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                    <span><?php echo e($project->start_date->format('M j, Y')); ?></span>
                                </div>
                            <?php endif; ?>
                            <?php if($project->end_date): ?>
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <span><?php echo e($project->end_date->format('M j, Y')); ?></span>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex gap-2">
                            <a href="<?php echo e(route('my-projects.show', $project)); ?>"
                               class="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200 text-center text-sm font-medium">
                                View Details
                            </a>

                            <?php if(in_array($project->status, ['planning', 'cancelled', 'on_hold'])): ?>
                                <form action="<?php echo e(route('my-projects.destroy', $project)); ?>" method="POST" class="inline-block"
                                      onsubmit="return confirm('Are you sure you want to delete this project? This action cannot be undone.')">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit"
                                            class="px-3 py-2 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors duration-200">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </form>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <!-- Pagination -->
        <?php if($projects->hasPages()): ?>
            <div class="mt-8">
                <?php echo e($projects->appends(request()->query())->links()); ?>

            </div>
        <?php endif; ?>
    <?php else: ?>
        <!-- Empty State -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
            <?php if(request()->filled('search') || request()->filled('status') || request()->filled('priority')): ?>
                <!-- No Results Found -->
                <svg class="mx-auto h-16 w-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
                <h3 class="mt-4 text-lg font-medium text-gray-900">No projects found</h3>
                <p class="mt-2 text-gray-500">No projects match your current search criteria. Try adjusting your filters or search terms.</p>
                <div class="mt-6">
                    <a href="<?php echo e(route('my-projects.index')); ?>" class="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        Clear All Filters
                    </a>
                </div>
            <?php else: ?>
                <!-- No Projects Yet -->
                <svg class="mx-auto h-16 w-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>
                <h3 class="mt-4 text-lg font-medium text-gray-900">No projects yet</h3>
                <p class="mt-2 text-gray-500">You don't have any projects yet. Contact us to start your first project and begin your digital transformation journey.</p>
                <div class="mt-6 space-y-3">
                    <a href="<?php echo e(route('contact')); ?>" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors duration-200">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                        </svg>
                        Start New Project
                    </a>
                    <div>
                        <a href="<?php echo e(route('services.index')); ?>" class="text-blue-600 hover:text-blue-700 font-medium">
                            Explore Our Services →
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>
</div>

<!-- Success/Error Messages -->
<?php if(session('success')): ?>
    <div class="fixed top-4 right-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg shadow-lg z-50" id="success-message">
        <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <?php echo e(session('success')); ?>

        </div>
    </div>
<?php endif; ?>

<?php if(session('error')): ?>
    <div class="fixed top-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg shadow-lg z-50" id="error-message">
        <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
            <?php echo e(session('error')); ?>

        </div>
    </div>
<?php endif; ?>

<script>
    // Auto-hide success/error messages after 5 seconds
    setTimeout(function() {
        const successMessage = document.getElementById('success-message');
        const errorMessage = document.getElementById('error-message');
        if (successMessage) successMessage.style.display = 'none';
        if (errorMessage) errorMessage.style.display = 'none';
    }, 5000);
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\files\chisolution\resources\views/projects/index.blade.php ENDPATH**/ ?>