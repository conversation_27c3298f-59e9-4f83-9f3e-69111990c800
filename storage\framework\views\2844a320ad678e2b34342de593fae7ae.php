<?php $__env->startSection('title', 'Order Complete - ' . __('common.company_name')); ?>
<?php $__env->startSection('meta_description', 'Thank you for your order! Your payment has been processed successfully.'); ?>

<?php $__env->startSection('content'); ?>
<!-- Order Success -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        <!-- Progress Steps -->
        <div class="max-w-4xl mx-auto mb-12">
            <div class="flex items-center justify-center space-x-4 md:space-x-8">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <span class="ml-2 text-sm font-medium text-green-600">Cart</span>
                </div>
                <div class="w-8 h-0.5 bg-green-600"></div>
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <span class="ml-2 text-sm font-medium text-green-600">Checkout</span>
                </div>
                <div class="w-8 h-0.5 bg-green-600"></div>
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <span class="ml-2 text-sm font-medium text-green-600">Complete</span>
                </div>
            </div>
        </div>

        <div class="max-w-4xl mx-auto">
            <!-- Success Message -->
            <div class="bg-white rounded-2xl shadow-lg p-8 text-center mb-8">
                <div class="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <svg class="w-10 h-10 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                
                <h1 class="heading-1 text-gray-900 mb-4">Order Complete!</h1>
                <p class="text-lead text-gray-600 mb-6">
                    Thank you for your order. Your payment has been processed successfully and we'll begin working on your project right away.
                </p>
                
                <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                    <div class="flex items-center justify-center">
                        <svg class="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-green-800 font-medium">
                            Order #<?php echo e($order->order_number); ?> - <?php echo e($order->formatted_total); ?>

                        </span>
                    </div>
                </div>
                
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="<?php echo e(route('orders.show', $order->uuid)); ?>" class="btn-primary">
                        View Order Details
                    </a>
                    <a href="<?php echo e(route('shop.index')); ?>" class="btn-outline">
                        Continue Shopping
                    </a>
                </div>
            </div>
            
            <!-- Order Summary -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Order Details -->
                <div class="bg-white rounded-2xl shadow-lg p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-6">Order Details</h2>
                    
                    <div class="space-y-4">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Order Number:</span>
                            <span class="font-medium text-gray-900">#<?php echo e($order->order_number); ?></span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600">Order Date:</span>
                            <span class="font-medium text-gray-900"><?php echo e($order->created_at->format('M d, Y')); ?></span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600">Payment Method:</span>
                            <span class="font-medium text-gray-900 capitalize"><?php echo e($order->payment_method); ?></span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600">Status:</span>
                            <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                                <?php echo e(ucfirst($order->status)); ?>

                            </span>
                        </div>
                        
                        <div class="border-t border-gray-200 pt-4">
                            <div class="flex justify-between text-lg font-semibold">
                                <span class="text-gray-900">Total:</span>
                                <span class="text-gray-900"><?php echo e($order->formatted_total); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Order Items -->
                <div class="bg-white rounded-2xl shadow-lg p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-6">Order Items</h2>
                    
                    <div class="space-y-4">
                        <?php $__currentLoopData = $order->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="flex items-center space-x-4 pb-4 border-b border-gray-200 last:border-b-0 last:pb-0">
                            <div class="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
                                <img src="<?php echo e($item->product->primary_image); ?>" alt="<?php echo e($item->product_name); ?>" 
                                     class="w-14 h-14 object-cover rounded">
                            </div>
                            <div class="flex-1 min-w-0">
                                <h3 class="text-sm font-medium text-gray-900 truncate"><?php echo e($item->product_name); ?></h3>
                                <?php if($item->variant_name): ?>
                                <p class="text-xs text-gray-500"><?php echo e($item->variant_name); ?></p>
                                <?php endif; ?>
                                <p class="text-xs text-gray-500">Qty: <?php echo e($item->quantity); ?></p>
                            </div>
                            <div class="text-sm font-medium text-gray-900">
                                <?php echo e($item->formatted_total_price); ?>

                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
            
            <!-- Next Steps -->
            <div class="bg-blue-50 border border-blue-200 rounded-2xl p-6 mt-8">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">What Happens Next?</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="text-center">
                        <div class="w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-3">
                            <span class="font-bold">1</span>
                        </div>
                        <h3 class="font-medium text-gray-900 mb-2">Confirmation Email</h3>
                        <p class="text-sm text-gray-600">You'll receive an order confirmation email within the next few minutes.</p>
                    </div>
                    
                    <div class="text-center">
                        <div class="w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-3">
                            <span class="font-bold">2</span>
                        </div>
                        <h3 class="font-medium text-gray-900 mb-2">Project Kickoff</h3>
                        <p class="text-sm text-gray-600">Our team will contact you within 24 hours to discuss your project requirements.</p>
                    </div>
                    
                    <div class="text-center">
                        <div class="w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-3">
                            <span class="font-bold">3</span>
                        </div>
                        <h3 class="font-medium text-gray-900 mb-2">Development Begins</h3>
                        <p class="text-sm text-gray-600">We'll start working on your project and keep you updated throughout the process.</p>
                    </div>
                </div>
            </div>
            
            <!-- Contact Information -->
            <div class="bg-white rounded-2xl shadow-lg p-6 mt-8 text-center">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Need Help?</h2>
                <p class="text-gray-600 mb-4">
                    If you have any questions about your order, please don't hesitate to contact us.
                </p>
                
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="<?php echo e(route('contact')); ?>" class="btn-outline">
                        Contact Support
                    </a>
                    <a href="mailto:<EMAIL>" class="btn-outline">
                        Email Us
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Track order completion for analytics
    if (typeof gtag !== 'undefined') {
        gtag('event', 'purchase', {
            'transaction_id': '<?php echo e($order->order_number); ?>',
            'value': <?php echo e($order->total); ?>,
            'currency': '<?php echo e($order->currency); ?>',
            'items': [
                <?php $__currentLoopData = $order->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                {
                    'item_id': '<?php echo e($item->product->sku); ?>',
                    'item_name': '<?php echo e($item->product_name); ?>',
                    'category': '<?php echo e($item->product->categories->first()->name ?? "General"); ?>',
                    'quantity': <?php echo e($item->quantity); ?>,
                    'price': <?php echo e($item->unit_price); ?>

                },
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            ]
        });
    }
    
    // Show success animation
    const successIcon = document.querySelector('.w-20.h-20 svg');
    if (successIcon) {
        successIcon.style.transform = 'scale(0)';
        setTimeout(() => {
            successIcon.style.transition = 'transform 0.5s ease-out';
            successIcon.style.transform = 'scale(1)';
        }, 300);
    }
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\files\chisolution\resources\views/pages/checkout/success.blade.php ENDPATH**/ ?>