<?php $__env->startSection('title', 'Checkout - ' . __('common.company_name')); ?>
<?php $__env->startSection('meta_description', 'Complete your purchase securely with our streamlined checkout process.'); ?>

<?php $__env->startSection('content'); ?>
<!-- Checkout Process -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        <!-- Progress Steps -->
        <div class="max-w-4xl mx-auto mb-12">
            <div class="flex items-center justify-center space-x-4 md:space-x-8">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">1</div>
                    <span class="ml-2 text-sm font-medium text-blue-600">Cart</span>
                </div>
                <div class="w-8 h-0.5 bg-blue-600"></div>
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">2</div>
                    <span class="ml-2 text-sm font-medium text-blue-600">Checkout</span>
                </div>
                <div class="w-8 h-0.5 bg-gray-300"></div>
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-gray-300 text-gray-500 rounded-full flex items-center justify-center text-sm font-medium">3</div>
                    <span class="ml-2 text-sm font-medium text-gray-500">Complete</span>
                </div>
            </div>
        </div>

        <div class="max-w-6xl mx-auto">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Checkout Form -->
                <div class="lg:col-span-2">
                    <div class="bg-white rounded-2xl shadow-lg p-8">
                        <h2 class="heading-2 mb-8">Checkout Details</h2>
                        
                        <form id="checkout-form" action="<?php echo e(route('checkout.process')); ?>" method="POST">
                            <?php echo csrf_field(); ?>
                            
                            <!-- Customer Information -->
                            <div class="mb-10">
                                <h3 class="text-lg font-semibold text-gray-900 mb-6">Customer Information</h3>
                                
                                <?php if(auth()->guard()->guest()): ?>
                                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                                    <div class="flex items-start">
                                        <svg class="w-5 h-5 text-blue-600 mt-0.5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                        </svg>
                                        <div>
                                            <p class="text-sm text-blue-800">
                                                <strong>Already have an account?</strong> 
                                                <a href="<?php echo e(route('login')); ?>" class="underline hover:no-underline">Sign in</a> 
                                                for faster checkout.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div class="floating-input-group">
                                        <input type="text" id="first_name" name="first_name" required 
                                               class="floating-input peer" placeholder=" " 
                                               value="<?php echo e(old('first_name', $user->first_name ?? '')); ?>">
                                        <label for="first_name" class="floating-label">First Name *</label>
                                    </div>
                                    
                                    <div class="floating-input-group">
                                        <input type="text" id="last_name" name="last_name" required 
                                               class="floating-input peer" placeholder=" " 
                                               value="<?php echo e(old('last_name', $user->last_name ?? '')); ?>">
                                        <label for="last_name" class="floating-label">Last Name *</label>
                                    </div>
                                </div>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div class="floating-input-group">
                                        <input type="email" id="email" name="email" required 
                                               class="floating-input peer" placeholder=" " 
                                               value="<?php echo e(old('email', $user->email ?? '')); ?>">
                                        <label for="email" class="floating-label">Email Address *</label>
                                    </div>
                                    
                                    <div class="floating-input-group">
                                        <input type="tel" id="phone" name="phone" 
                                               class="floating-input peer" placeholder=" " 
                                               value="<?php echo e(old('phone', $user->phone ?? '')); ?>">
                                        <label for="phone" class="floating-label">Phone Number</label>
                                    </div>
                                </div>
                                
                                <?php if(auth()->guard()->guest()): ?>
                                <div class="mt-6">
                                    <label class="flex items-center">
                                        <input type="checkbox" name="create_account" value="1" 
                                               class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                        <span class="ml-2 text-sm text-gray-700">Create an account for faster future checkouts</span>
                                    </label>
                                </div>
                                
                                <div id="password-fields" class="hidden mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div class="floating-input-group">
                                        <input type="password" id="password" name="password" 
                                               class="floating-input peer" placeholder=" ">
                                        <label for="password" class="floating-label">Password</label>
                                    </div>
                                    
                                    <div class="floating-input-group">
                                        <input type="password" id="password_confirmation" name="password_confirmation" 
                                               class="floating-input peer" placeholder=" ">
                                        <label for="password_confirmation" class="floating-label">Confirm Password</label>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                            
                            <!-- Billing Address -->
                            <div class="mb-10">
                                <h3 class="text-lg font-semibold text-gray-900 mb-6">Billing Address</h3>
                                
                                <div class="space-y-6">
                                    <div class="floating-input-group">
                                        <input type="text" id="billing_address_line_1" name="billing_address_line_1" required 
                                               class="floating-input peer" placeholder=" " 
                                               value="<?php echo e(old('billing_address_line_1')); ?>">
                                        <label for="billing_address_line_1" class="floating-label">Address Line 1 *</label>
                                    </div>
                                    
                                    <div class="floating-input-group">
                                        <input type="text" id="billing_address_line_2" name="billing_address_line_2" 
                                               class="floating-input peer" placeholder=" " 
                                               value="<?php echo e(old('billing_address_line_2')); ?>">
                                        <label for="billing_address_line_2" class="floating-label">Address Line 2</label>
                                    </div>
                                    
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                        <div class="floating-input-group">
                                            <input type="text" id="billing_city" name="billing_city" required 
                                                   class="floating-input peer" placeholder=" " 
                                                   value="<?php echo e(old('billing_city')); ?>">
                                            <label for="billing_city" class="floating-label">City *</label>
                                        </div>
                                        
                                        <div class="floating-input-group">
                                            <input type="text" id="billing_state" name="billing_state" 
                                                   class="floating-input peer" placeholder=" " 
                                                   value="<?php echo e(old('billing_state')); ?>">
                                            <label for="billing_state" class="floating-label">State/Province</label>
                                        </div>
                                        
                                        <div class="floating-input-group">
                                            <input type="text" id="billing_postal_code" name="billing_postal_code" required 
                                                   class="floating-input peer" placeholder=" " 
                                                   value="<?php echo e(old('billing_postal_code')); ?>">
                                            <label for="billing_postal_code" class="floating-label">Postal Code *</label>
                                        </div>
                                    </div>
                                    
                                    <div class="floating-input-group">
                                        <select id="billing_country" name="billing_country" required class="floating-input peer">
                                            <option value=""></option>
                                            <option value="ZA" <?php echo e(old('billing_country') === 'ZA' ? 'selected' : ''); ?>>South Africa</option>
                                            <option value="US" <?php echo e(old('billing_country') === 'US' ? 'selected' : ''); ?>>United States</option>
                                            <option value="GB" <?php echo e(old('billing_country') === 'GB' ? 'selected' : ''); ?>>United Kingdom</option>
                                            <option value="CA" <?php echo e(old('billing_country') === 'CA' ? 'selected' : ''); ?>>Canada</option>
                                            <option value="AU" <?php echo e(old('billing_country') === 'AU' ? 'selected' : ''); ?>>Australia</option>
                                        </select>
                                        <label for="billing_country" class="floating-label">Country *</label>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Shipping Address -->
                            <div class="mb-10">
                                <div class="flex items-center justify-between mb-6">
                                    <h3 class="text-lg font-semibold text-gray-900">Shipping Address</h3>
                                    <label class="flex items-center">
                                        <input type="checkbox" id="same_as_billing" name="same_as_billing" value="1" checked
                                               class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                        <span class="ml-2 text-sm text-gray-700">Same as billing address</span>
                                    </label>
                                </div>
                                
                                <div id="shipping-address-fields" class="hidden space-y-6">
                                    <div class="floating-input-group">
                                        <input type="text" id="shipping_address_line_1" name="shipping_address_line_1" 
                                               class="floating-input peer" placeholder=" " 
                                               value="<?php echo e(old('shipping_address_line_1')); ?>">
                                        <label for="shipping_address_line_1" class="floating-label">Address Line 1</label>
                                    </div>
                                    
                                    <div class="floating-input-group">
                                        <input type="text" id="shipping_address_line_2" name="shipping_address_line_2" 
                                               class="floating-input peer" placeholder=" " 
                                               value="<?php echo e(old('shipping_address_line_2')); ?>">
                                        <label for="shipping_address_line_2" class="floating-label">Address Line 2</label>
                                    </div>
                                    
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                        <div class="floating-input-group">
                                            <input type="text" id="shipping_city" name="shipping_city" 
                                                   class="floating-input peer" placeholder=" " 
                                                   value="<?php echo e(old('shipping_city')); ?>">
                                            <label for="shipping_city" class="floating-label">City</label>
                                        </div>
                                        
                                        <div class="floating-input-group">
                                            <input type="text" id="shipping_state" name="shipping_state" 
                                                   class="floating-input peer" placeholder=" " 
                                                   value="<?php echo e(old('shipping_state')); ?>">
                                            <label for="shipping_state" class="floating-label">State/Province</label>
                                        </div>
                                        
                                        <div class="floating-input-group">
                                            <input type="text" id="shipping_postal_code" name="shipping_postal_code" 
                                                   class="floating-input peer" placeholder=" " 
                                                   value="<?php echo e(old('shipping_postal_code')); ?>">
                                            <label for="shipping_postal_code" class="floating-label">Postal Code</label>
                                        </div>
                                    </div>
                                    
                                    <div class="floating-input-group">
                                        <select id="shipping_country" name="shipping_country" class="floating-input peer">
                                            <option value=""></option>
                                            <option value="ZA" <?php echo e(old('shipping_country') === 'ZA' ? 'selected' : ''); ?>>South Africa</option>
                                            <option value="US" <?php echo e(old('shipping_country') === 'US' ? 'selected' : ''); ?>>United States</option>
                                            <option value="GB" <?php echo e(old('shipping_country') === 'GB' ? 'selected' : ''); ?>>United Kingdom</option>
                                            <option value="CA" <?php echo e(old('shipping_country') === 'CA' ? 'selected' : ''); ?>>Canada</option>
                                            <option value="AU" <?php echo e(old('shipping_country') === 'AU' ? 'selected' : ''); ?>>Australia</option>
                                        </select>
                                        <label for="shipping_country" class="floating-label">Country</label>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Order Notes -->
                            <div class="mb-10">
                                <h3 class="text-lg font-semibold text-gray-900 mb-6">Order Notes</h3>
                                <div class="floating-input-group">
                                    <textarea id="order_notes" name="order_notes" rows="4" 
                                              class="floating-input peer" placeholder=" "><?php echo e(old('order_notes')); ?></textarea>
                                    <label for="order_notes" class="floating-label">Special instructions or notes (optional)</label>
                                </div>
                            </div>
                            
                            <!-- Payment Method -->
                            <div class="mb-10">
                                <h3 class="text-lg font-semibold text-gray-900 mb-6">Payment Method</h3>
                                
                                <div class="space-y-4">
                                    <label class="flex items-start p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                                        <input type="radio" name="payment_method" value="stripe" checked 
                                               class="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500 mt-1">
                                        <div class="ml-3">
                                            <div class="flex items-center">
                                                <span class="text-sm font-medium text-gray-900">Credit/Debit Card</span>
                                                <div class="ml-2 flex space-x-1">
                                                    <img src="https://js.stripe.com/v3/fingerprinted/img/visa-729c05c240c4bdb47b03ac81d9945bfe.svg" alt="Visa" class="h-4">
                                                    <img src="https://js.stripe.com/v3/fingerprinted/img/mastercard-4d8844094130711885b5e41b28c9848f.svg" alt="Mastercard" class="h-4">
                                                    <img src="https://js.stripe.com/v3/fingerprinted/img/amex-a49b82f46c5cd6a96a6e418a6ca1717c.svg" alt="American Express" class="h-4">
                                                </div>
                                            </div>
                                            <p class="text-xs text-gray-500 mt-1">Secure payment via Stripe</p>
                                        </div>
                                    </label>
                                    
                                    <label class="flex items-start p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                                        <input type="radio" name="payment_method" value="paypal" 
                                               class="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500 mt-1">
                                        <div class="ml-3">
                                            <div class="flex items-center">
                                                <span class="text-sm font-medium text-gray-900">PayPal</span>
                                                <img src="https://www.paypalobjects.com/webstatic/mktg/Logo/pp-logo-100px.png" alt="PayPal" class="h-4 ml-2">
                                            </div>
                                            <p class="text-xs text-gray-500 mt-1">Pay with your PayPal account</p>
                                        </div>
                                    </label>
                                </div>
                                
                                <!-- Stripe Card Element -->
                                <div id="stripe-card-section" class="mt-6">
                                    <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                                        <div id="stripe-card-element" class="p-3 bg-white border border-gray-300 rounded-md">
                                            <!-- Stripe Elements will create form elements here -->
                                        </div>
                                        <div id="stripe-card-errors" class="text-red-600 text-sm mt-2" role="alert"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Terms and Conditions -->
                            <div class="mb-8">
                                <label class="flex items-start">
                                    <input type="checkbox" name="terms_accepted" value="1" required 
                                           class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 mt-1">
                                    <span class="ml-2 text-sm text-gray-700">
                                        I agree to the <a href="#" class="text-blue-600 hover:text-blue-700 underline">Terms and Conditions</a> 
                                        and <a href="#" class="text-blue-600 hover:text-blue-700 underline">Privacy Policy</a> *
                                    </span>
                                </label>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Order Summary -->
                <div class="lg:col-span-1">
                    <div class="bg-white rounded-2xl shadow-lg p-6 sticky top-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-6">Order Summary</h3>
                        
                        <!-- Cart Items -->
                        <div class="space-y-4 mb-6" id="checkout-cart-items">
                            <?php $__currentLoopData = $cart->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="cart-item border border-gray-200 rounded-lg p-3" data-item-id="<?php echo e($item->id); ?>">
                                <div class="flex items-start space-x-3">
                                    <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                                        <img src="<?php echo e($item->product->primary_image); ?>" alt="<?php echo e($item->product->name); ?>"
                                             class="w-10 h-10 object-cover rounded">
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <h4 class="text-sm font-medium text-gray-900 truncate"><?php echo e($item->product->name); ?></h4>
                                        <?php if($item->productVariant): ?>
                                            <p class="text-xs text-gray-500"><?php echo e($item->productVariant->name); ?></p>
                                        <?php endif; ?>

                                        <!-- Quantity Controls -->
                                        <div class="flex items-center mt-2 space-x-2">
                                            <button type="button" class="quantity-btn quantity-decrease w-6 h-6 rounded-full border border-gray-300 flex items-center justify-center text-gray-500 hover:bg-gray-100 transition-colors"
                                                    data-item-id="<?php echo e($item->id); ?>">
                                                <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                                                </svg>
                                            </button>
                                            <span class="item-quantity text-sm font-medium text-gray-900 min-w-[2rem] text-center"><?php echo e($item->quantity); ?></span>
                                            <button type="button" class="quantity-btn quantity-increase w-6 h-6 rounded-full border border-gray-300 flex items-center justify-center text-gray-500 hover:bg-gray-100 transition-colors"
                                                    data-item-id="<?php echo e($item->id); ?>">
                                                <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-sm font-medium text-gray-900 item-total">
                                            <?php echo e($item->formatted_total); ?>

                                        </div>
                                        <button type="button" class="remove-item text-xs text-red-600 hover:text-red-800 mt-1"
                                                data-item-id="<?php echo e($item->id); ?>">
                                            Remove
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                        
                        <!-- Coupon Section -->
                        <?php if(!$cart->coupon_code): ?>
                        <div class="border-t border-gray-200 pt-4 mb-4">
                            <form id="checkout-coupon-form" class="space-y-3">
                                <div>
                                    <label for="checkout_coupon_code" class="block text-sm font-medium text-gray-700 mb-1">
                                        Coupon Code
                                    </label>
                                    <div class="flex">
                                        <input type="text" id="checkout_coupon_code" name="coupon_code"
                                               class="flex-1 px-3 py-2 border border-gray-300 rounded-l-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                                               placeholder="Enter coupon code">
                                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white text-sm rounded-r-md hover:bg-blue-700 transition-colors">
                                            Apply
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <?php endif; ?>

                        <!-- Order Totals -->
                        <div class="border-t border-gray-200 pt-4 space-y-3">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Subtotal</span>
                                <span class="text-gray-900 order-subtotal"><?php echo e($cart->formatted_subtotal); ?></span>
                            </div>

                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Shipping</span>
                                <span class="text-gray-900">Free</span>
                            </div>

                            <?php if($cart->discount_amount > 0): ?>
                            <div class="flex justify-between text-sm text-green-600">
                                <span>Discount (<?php echo e($cart->coupon_code); ?>)</span>
                                <span class="order-discount">-<?php echo e($cart->formatted_discount_amount); ?></span>
                            </div>
                            <?php endif; ?>

                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Tax</span>
                                <span class="text-gray-900 order-tax"><?php echo e($cart->formatted_tax_amount); ?></span>
                            </div>

                            <div class="border-t border-gray-200 pt-3">
                                <div class="flex justify-between">
                                    <span class="text-base font-semibold text-gray-900">Total</span>
                                    <span class="text-lg font-bold text-gray-900 order-total"><?php echo e($cart->formatted_total); ?></span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Place Order Button -->
                        <button type="submit" form="checkout-form" 
                                class="w-full mt-6 btn-primary transform hover:scale-105 transition-all duration-200">
                            <span class="flex items-center justify-center">
                                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                                </svg>
                                Place Order Securely
                            </span>
                        </button>
                        
                        <!-- Security Badges -->
                        <div class="mt-4 text-center">
                            <div class="flex items-center justify-center space-x-2 text-xs text-gray-500">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span>SSL Encrypted</span>
                                <span>•</span>
                                <span>Secure Checkout</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php $__env->startPush('scripts'); ?>
<script src="https://js.stripe.com/v3/"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Floating label functionality
    const floatingInputs = document.querySelectorAll('.floating-input');
    
    floatingInputs.forEach(input => {
        if (input.value.trim() !== '') {
            input.classList.add('has-value');
        }
        
        input.addEventListener('input', function() {
            if (this.value.trim() !== '') {
                this.classList.add('has-value');
            } else {
                this.classList.remove('has-value');
            }
        });
        
        input.addEventListener('focus', function() {
            this.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            this.classList.remove('focused');
        });
        
        if (input.tagName === 'SELECT') {
            input.addEventListener('change', function() {
                if (this.value !== '') {
                    this.classList.add('has-value');
                } else {
                    this.classList.remove('has-value');
                }
            });
        }
    });
    
    // Create account checkbox
    const createAccountCheckbox = document.querySelector('input[name="create_account"]');
    const passwordFields = document.getElementById('password-fields');
    
    if (createAccountCheckbox) {
        createAccountCheckbox.addEventListener('change', function() {
            if (this.checked) {
                passwordFields.classList.remove('hidden');
                document.getElementById('password').required = true;
                document.getElementById('password_confirmation').required = true;
            } else {
                passwordFields.classList.add('hidden');
                document.getElementById('password').required = false;
                document.getElementById('password_confirmation').required = false;
            }
        });
    }
    
    // Same as billing address
    const sameAsBillingCheckbox = document.getElementById('same_as_billing');
    const shippingAddressFields = document.getElementById('shipping-address-fields');
    
    sameAsBillingCheckbox.addEventListener('change', function() {
        if (this.checked) {
            shippingAddressFields.classList.add('hidden');
        } else {
            shippingAddressFields.classList.remove('hidden');
        }
    });
    
    // Payment method selection
    const paymentMethods = document.querySelectorAll('input[name="payment_method"]');
    const stripeCardSection = document.getElementById('stripe-card-section');
    
    paymentMethods.forEach(method => {
        method.addEventListener('change', function() {
            if (this.value === 'stripe') {
                stripeCardSection.style.display = 'block';
            } else {
                stripeCardSection.style.display = 'none';
            }
        });
    });
    
    // Initialize Stripe
    const stripe = Stripe('<?php echo e(config("services.stripe.key")); ?>');
    const elements = stripe.elements();
    
    const cardElement = elements.create('card', {
        style: {
            base: {
                fontSize: '16px',
                color: '#374151',
                '::placeholder': {
                    color: '#9ca3af',
                },
            },
        },
    });
    
    cardElement.mount('#stripe-card-element');
    
    cardElement.on('change', function(event) {
        const displayError = document.getElementById('stripe-card-errors');
        if (event.error) {
            displayError.textContent = event.error.message;
        } else {
            displayError.textContent = '';
        }
    });
    
    // Checkout coupon form handler
    document.getElementById('checkout-coupon-form')?.addEventListener('submit', function(e) {
        e.preventDefault();
        applyCheckoutCoupon();
    });

    // Form submission
    const form = document.getElementById('checkout-form');
    form.addEventListener('submit', async function(event) {
        event.preventDefault();
        
        const submitButton = form.querySelector('button[type="submit"]');
        const originalText = submitButton.innerHTML;
        
        submitButton.innerHTML = `
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Processing...
        `;
        submitButton.disabled = true;
        
        const selectedPaymentMethod = document.querySelector('input[name="payment_method"]:checked').value;
        
        if (selectedPaymentMethod === 'stripe') {
            const {token, error} = await stripe.createToken(cardElement);
            
            if (error) {
                document.getElementById('stripe-card-errors').textContent = error.message;
                submitButton.innerHTML = originalText;
                submitButton.disabled = false;
            } else {
                // Add token to form
                const tokenInput = document.createElement('input');
                tokenInput.type = 'hidden';
                tokenInput.name = 'stripe_token';
                tokenInput.value = token.id;
                form.appendChild(tokenInput);
                
                // Submit form
                form.submit();
            }
        } else {
            // For other payment methods, submit directly
            form.submit();
        }
    });

    // Cart quantity update functionality
    const quantityButtons = document.querySelectorAll('.quantity-btn');
    const removeButtons = document.querySelectorAll('.remove-item');

    quantityButtons.forEach(button => {
        button.addEventListener('click', function() {
            const itemId = this.dataset.itemId;
            const isIncrease = this.classList.contains('quantity-increase');
            const cartItem = document.querySelector(`.cart-item[data-item-id="${itemId}"]`);
            const quantitySpan = cartItem.querySelector('.item-quantity');
            const currentQuantity = parseInt(quantitySpan.textContent);

            let newQuantity;
            if (isIncrease) {
                newQuantity = currentQuantity + 1;
            } else {
                newQuantity = Math.max(0, currentQuantity - 1);
            }

            console.log('🔄 Updating cart item quantity', {
                itemId: itemId,
                currentQuantity: currentQuantity,
                newQuantity: newQuantity,
                isIncrease: isIncrease
            });

            updateCartItemQuantity(itemId, newQuantity, cartItem);
        });
    });

    removeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const itemId = this.dataset.itemId;
            const cartItem = document.querySelector(`.cart-item[data-item-id="${itemId}"]`);

            console.log('🗑️ Removing cart item', { itemId: itemId });
            updateCartItemQuantity(itemId, 0, cartItem);
        });
    });

    async function updateCartItemQuantity(itemId, quantity, cartItemElement) {
        try {
            const response = await fetch(`/cart/update/${itemId}`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ quantity: quantity })
            });

            const data = await response.json();
            console.log('📡 Cart update response:', data);

            if (data.success) {
                if (quantity === 0) {
                    // Remove item from DOM
                    cartItemElement.remove();
                    console.log('✅ Item removed from cart');
                } else {
                    // Update quantity and total in DOM
                    const quantitySpan = cartItemElement.querySelector('.item-quantity');
                    const totalSpan = cartItemElement.querySelector('.item-total');

                    quantitySpan.textContent = data.item.quantity;
                    totalSpan.textContent = data.item.formatted_total;
                    console.log('✅ Item quantity updated');
                }

                // Update cart totals
                updateCartTotals(data.cart);

                // Update global cart count using global function
                updateCartCount(data.cart.item_count);

                // Check if cart is empty and redirect to shop
                if (data.cart.item_count === 0) {
                    console.log('🛒 Cart is empty, redirecting to shop');
                    window.location.href = '/shop';
                }
            } else {
                console.error('❌ Cart update failed:', data.message);
                alert(data.message || 'Failed to update cart item');
            }
        } catch (error) {
            console.error('❌ Error updating cart:', error);
            alert('An error occurred while updating the cart');
        }
    }

    function updateCartTotals(cartData) {
        // Update subtotal, tax, and total in the order summary
        const subtotalElement = document.querySelector('.order-subtotal');
        const taxElement = document.querySelector('.order-tax');
        const totalElement = document.querySelector('.order-total');

        if (subtotalElement) subtotalElement.textContent = cartData.formatted_subtotal || cartData.formatted_total;
        if (taxElement) taxElement.textContent = cartData.formatted_tax_amount || 'R 0.00';
        if (totalElement) totalElement.textContent = cartData.formatted_total;

        console.log('💰 Cart totals updated', cartData);
    }

    function applyCheckoutCoupon() {
        const couponCode = document.getElementById('checkout_coupon_code').value.trim();

        if (!couponCode) {
            alert('Please enter a coupon code.');
            return;
        }

        const submitBtn = document.querySelector('#checkout-coupon-form button[type="submit"]');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = 'Applying...';
        submitBtn.disabled = true;

        fetch('/cart/coupon', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({ coupon_code: couponCode })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Reload page to show applied coupon
                location.reload();
            } else {
                alert(data.message);
            }
        })
        .catch(error => {
            alert('An error occurred. Please try again.');
        })
        .finally(() => {
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        });
    }
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\files\chisolution\resources\views/pages/checkout/index.blade.php ENDPATH**/ ?>