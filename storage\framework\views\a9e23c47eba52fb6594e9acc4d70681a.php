<?php $__env->startSection('title', ($category->meta_title ?: $category->name) . ' - ' . __('common.company_name')); ?>
<?php $__env->startSection('meta_description', $category->meta_description ?: $category->description ?: 'Browse products in ' . $category->name . ' category'); ?>
<?php $__env->startSection('meta_keywords', $category->meta_keywords ?: $category->name . ', products, shop'); ?>

<?php $__env->startSection('og_title', ($category->meta_title ?: $category->name) . ' - ' . __('common.company_name')); ?>
<?php $__env->startSection('og_description', $category->meta_description ?: $category->description ?: 'Browse products in ' . $category->name . ' category'); ?>
<?php $__env->startSection('og_image', $category->image ? asset('storage/' . $category->image) : asset('images/og-image.jpg')); ?>

<?php $__env->startSection('twitter_title', ($category->meta_title ?: $category->name) . ' - ' . __('common.company_name')); ?>
<?php $__env->startSection('twitter_description', $category->meta_description ?: $category->description ?: 'Browse products in ' . $category->name . ' category'); ?>
<?php $__env->startSection('twitter_image', $category->image ? asset('storage/' . $category->image) : asset('images/twitter-image.jpg')); ?>

<?php $__env->startSection('content'); ?>
<!-- Category Hero -->
<section class="relative bg-gradient-blue text-white overflow-hidden">
    <?php if($category->image): ?>
        <div class="absolute inset-0 opacity-20">
            <img src="<?php echo e(asset('storage/' . $category->image)); ?>" alt="<?php echo e($category->name); ?>" class="w-full h-full object-cover">
        </div>
        <div class="absolute inset-0 bg-gradient-to-r from-blue-900/80 to-blue-600/80"></div>
    <?php else: ?>
        <div class="absolute inset-0 opacity-10">
            <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></g></svg>');"></div>
        </div>
    <?php endif; ?>

    <div class="container mx-auto px-4 py-16 relative z-10">
        <div class="text-center max-w-4xl mx-auto">
            <?php if($category->is_featured): ?>
                <div class="inline-flex items-center px-4 py-2 bg-yellow-500/20 border border-yellow-400/30 rounded-full text-yellow-200 text-sm font-medium mb-4">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"/>
                    </svg>
                    Featured Category
                </div>
            <?php endif; ?>
            <!-- Breadcrumbs -->
            <nav class="mb-6">
                <ol class="flex items-center justify-center space-x-2 text-blue-200">
                    <li><a href="<?php echo e(route('shop.index')); ?>" class="hover:text-white transition-colors">Shop</a></li>
                    <?php $__currentLoopData = $category->breadcrumbs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $breadcrumb): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li class="flex items-center">
                        <svg class="w-4 h-4 mx-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <?php if($loop->last): ?>
                        <span class="text-white font-medium"><?php echo e($breadcrumb['name']); ?></span>
                        <?php else: ?>
                        <a href="<?php echo e($breadcrumb['url']); ?>" class="hover:text-white transition-colors"><?php echo e($breadcrumb['name']); ?></a>
                        <?php endif; ?>
                    </li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ol>
            </nav>
            
            <h1 class="heading-1 text-white mb-4"><?php echo e($category->name); ?></h1>
            <?php if($category->description): ?>
            <p class="text-lead text-blue-100"><?php echo e($category->description); ?></p>
            <?php endif; ?>
        </div>
    </div>
    
    <div class="absolute bottom-0 left-0 w-full">
        <svg viewBox="0 0 1200 120" preserveAspectRatio="none" class="relative block w-full h-16 fill-white">
            <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z"></path>
        </svg>
    </div>
</section>

<!-- Subcategories -->
<?php if($subcategories->count() > 0): ?>
<section class="py-12 bg-gray-50">
    <div class="container mx-auto px-4">
        <h2 class="text-xl font-semibold text-gray-900 mb-6">Browse Subcategories</h2>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <?php $__currentLoopData = $subcategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subcategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <a href="<?php echo e(route('shop.category', $subcategory->slug)); ?>" 
               class="bg-white rounded-lg p-4 text-center hover:shadow-md transition-shadow">
                <h3 class="font-medium text-gray-900 mb-1"><?php echo e($subcategory->name); ?></h3>
                <p class="text-sm text-gray-500">(<?php echo e($subcategory->products_count); ?> products)</p>
            </a>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Products -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <!-- Sidebar Filters -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-lg shadow-sm p-6 sticky top-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">Filters</h3>
                    
                    <form action="<?php echo e(route('shop.category', $category->slug)); ?>" method="GET" id="filter-form">
                        <?php if(request('search')): ?>
                            <input type="hidden" name="search" value="<?php echo e(request('search')); ?>">
                        <?php endif; ?>
                        
                        <!-- Price Range -->
                        <?php if($priceRange && $priceRange->min_price !== $priceRange->max_price): ?>
                        <div class="mb-8">
                            <h4 class="font-medium text-gray-900 mb-4">Price Range</h4>
                            <div class="space-y-4">
                                <div class="grid grid-cols-2 gap-2">
                                    <div>
                                        <label class="block text-xs text-gray-600 mb-1">Min Price</label>
                                        <input type="number" name="min_price" value="<?php echo e(request('min_price')); ?>" 
                                               min="<?php echo e($priceRange->min_price); ?>" max="<?php echo e($priceRange->max_price); ?>"
                                               placeholder="R<?php echo e(number_format($priceRange->min_price, 0)); ?>"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500">
                                    </div>
                                    <div>
                                        <label class="block text-xs text-gray-600 mb-1">Max Price</label>
                                        <input type="number" name="max_price" value="<?php echo e(request('max_price')); ?>" 
                                               min="<?php echo e($priceRange->min_price); ?>" max="<?php echo e($priceRange->max_price); ?>"
                                               placeholder="R<?php echo e(number_format($priceRange->max_price, 0)); ?>"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500">
                                    </div>
                                </div>
                                <div class="text-xs text-gray-500">
                                    Range: R<?php echo e(number_format($priceRange->min_price, 0)); ?> - R<?php echo e(number_format($priceRange->max_price, 0)); ?>

                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <!-- Filter Actions -->
                        <div class="space-y-3">
                            <button type="submit" class="w-full btn-primary">
                                Apply Filters
                            </button>
                            <a href="<?php echo e(route('shop.category', $category->slug)); ?>" class="w-full btn-outline text-center block">
                                Clear Filters
                            </a>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Products Grid -->
            <div class="lg:col-span-3">
                <!-- Sort and Results Info -->
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
                    <div class="text-gray-600 mb-4 sm:mb-0">
                        Showing <?php echo e($products->firstItem() ?? 0); ?>-<?php echo e($products->lastItem() ?? 0); ?> of <?php echo e($products->total()); ?> products
                        <?php if(request('search')): ?>
                            for "<strong><?php echo e(request('search')); ?></strong>"
                        <?php endif; ?>
                        in <?php echo e($category->name); ?>

                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <label class="text-sm text-gray-600">Sort by:</label>
                        <select name="sort" onchange="updateSort(this)" class="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500">
                            <option value="name" <?php echo e(request('sort') === 'name' ? 'selected' : ''); ?>>Name</option>
                            <option value="price" <?php echo e(request('sort') === 'price' ? 'selected' : ''); ?>>Price</option>
                            <option value="created_at" <?php echo e(request('sort') === 'created_at' ? 'selected' : ''); ?>>Newest</option>
                            <option value="featured" <?php echo e(request('sort') === 'featured' ? 'selected' : ''); ?>>Featured</option>
                        </select>
                    </div>
                </div>
                
                <?php if($products->count() > 0): ?>
                <!-- Products Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 mb-8">
                    <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="product-card bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow">
                        <div class="relative">
                            <a href="<?php echo e(route('shop.product', $product->slug)); ?>">
                                <img src="<?php echo e($product->primary_image); ?>" alt="<?php echo e($product->name); ?>" 
                                     class="w-full h-48 object-cover hover-lift">
                            </a>
                            
                            <?php if($product->discount_percentage > 0): ?>
                            <div class="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-xs font-medium">
                                -<?php echo e($product->discount_percentage); ?>%
                            </div>
                            <?php endif; ?>
                            
                            <?php if($product->is_featured): ?>
                            <div class="absolute top-2 right-2 bg-blue-600 text-white px-2 py-1 rounded text-xs font-medium">
                                Featured
                            </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="p-4">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                <a href="<?php echo e(route('shop.product', $product->slug)); ?>" class="hover:text-blue-600 transition-colors">
                                    <?php echo e($product->name); ?>

                                </a>
                            </h3>
                            
                            <?php if($product->short_description): ?>
                            <div class="text-gray-600 text-sm mb-3 line-clamp-2 prose prose-sm max-w-none">
                                <?php echo $product->short_description; ?>

                            </div>
                            <?php endif; ?>
                            
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <span class="text-lg font-bold text-gray-900"><?php echo e($product->formatted_price); ?></span>
                                    <?php if($product->compare_price): ?>
                                    <span class="text-sm text-gray-500 line-through"><?php echo e($product->formatted_compare_price); ?></span>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="text-xs text-gray-500">
                                    <?php echo e($product->stock_status); ?>

                                </div>
                            </div>
                            
                            <div class="mt-4">
                                <a href="<?php echo e(route('shop.product', $product->slug)); ?>" class="w-full btn-primary text-center block">
                                    View Details
                                </a>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
                
                <!-- Pagination -->
                <div class="flex justify-center">
                    <?php echo e($products->links()); ?>

                </div>
                <?php else: ?>
                <!-- No Products Found -->
                <div class="text-center py-12">
                    <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-12 h-12 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No products found</h3>
                    <p class="text-gray-600 mb-4">
                        No products match your current filters in this category.
                    </p>
                    <a href="<?php echo e(route('shop.index')); ?>" class="btn-primary">
                        Browse All Products
                    </a>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<?php $__env->startPush('scripts'); ?>
<script>
function updateSort(select) {
    const url = new URL(window.location);
    url.searchParams.set('sort', select.value);
    window.location.href = url.toString();
}

// Auto-submit form when filters change
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('filter-form');
    const inputs = form.querySelectorAll('input[type="number"]');
    
    inputs.forEach(input => {
        input.addEventListener('change', function() {
            // Add a small delay to allow user to finish typing
            setTimeout(() => {
                form.submit();
            }, 500);
        });
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('structured_data'); ?>
<script type="application/ld+json">
{
    "@context": "https://schema.org/",
    "@type": "CollectionPage",
    "name": "<?php echo e($category->name); ?>",
    "description": "<?php echo e($category->meta_description ?: $category->description ?: 'Browse products in ' . $category->name . ' category'); ?>",
    "url": "<?php echo e(route('shop.category', $category->slug)); ?>",
    <?php if($category->image): ?>
    "image": "<?php echo e(asset('storage/' . $category->image)); ?>",
    <?php endif; ?>
    "mainEntity": {
        "@type": "ItemList",
        "numberOfItems": "<?php echo e($products->total()); ?>",
        "itemListElement": [
            <?php $__currentLoopData = $products->take(10); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            {
                "@type": "Product",
                "position": <?php echo e($index + 1); ?>,
                "name": "<?php echo e($product->name); ?>",
                "url": "<?php echo e(route('shop.product', $product->slug)); ?>",
                "image": "<?php echo e($product->featured_image ? asset('storage/' . $product->featured_image) : $product->primary_image); ?>",
                "offers": {
                    "@type": "Offer",
                    "price": "<?php echo e($product->price); ?>",
                    "priceCurrency": "ZAR",
                    "availability": "<?php echo e($product->isInStock() ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock'); ?>"
                }
            }<?php if(!$loop->last): ?>,<?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        ]
    },
    "breadcrumb": {
        "@type": "BreadcrumbList",
        "itemListElement": [
            {
                "@type": "ListItem",
                "position": 1,
                "name": "Shop",
                "item": "<?php echo e(route('shop.index')); ?>"
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": "<?php echo e($category->name); ?>",
                "item": "<?php echo e(route('shop.category', $category->slug)); ?>"
            }
        ]
    }
}
</script>
<?php $__env->stopPush(); ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\files\chisolution\resources\views/pages/shop/category.blade.php ENDPATH**/ ?>