<?php

namespace Database\Factories;

use App\Models\Job;
use App\Models\JobApplication;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\JobApplication>
 */
class JobApplicationFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = JobApplication::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'uuid' => Str::uuid(),
            'job_id' => Job::factory(),
            'user_id' => null, // Default to guest application
            'reference_number' => 'JOB-' . strtoupper($this->faker->bothify('??##??##')),
            'first_name' => $this->faker->firstName(),
            'last_name' => $this->faker->lastName(),
            'email' => $this->faker->email(),
            'phone' => $this->faker->optional()->phoneNumber(),
            'cover_letter' => $this->faker->paragraphs(3, true),
            'resume_path' => $this->faker->optional()->filePath(),
            'portfolio_url' => $this->faker->optional()->url(),
            'linkedin_url' => $this->faker->optional()->url(),
            'expected_salary' => $this->faker->optional()->randomFloat(2, 30000, 150000),
            'availability_date' => $this->faker->optional()->dateTimeBetween('now', '+3 months'),
            'status' => $this->faker->randomElement(['pending', 'reviewing', 'shortlisted', 'interviewed', 'rejected', 'hired']),
            'admin_notes' => $this->faker->optional()->paragraph(),
            'reviewed_by' => null,
            'reviewed_at' => null,
            'is_deleted' => false,
        ];
    }

    /**
     * Create an application for a specific job.
     */
    public function forJob(Job $job): static
    {
        return $this->state(fn (array $attributes) => [
            'job_id' => $job->id,
        ]);
    }

    /**
     * Create an authenticated user application.
     */
    public function authenticated(User $user = null): static
    {
        return $this->state(fn (array $attributes) => [
            'user_id' => $user?->id ?? User::factory()->create()->id,
        ]);
    }

    /**
     * Create a guest application.
     */
    public function guest(): static
    {
        return $this->state(fn (array $attributes) => [
            'user_id' => null,
        ]);
    }

    /**
     * Create a pending application.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
            'reviewed_by' => null,
            'reviewed_at' => null,
        ]);
    }

    /**
     * Create a reviewed application.
     */
    public function reviewed(string $status = 'reviewing'): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => $status,
            'reviewed_by' => User::factory()->create()->id,
            'reviewed_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
            'admin_notes' => $this->faker->paragraph(),
        ]);
    }

    /**
     * Create a shortlisted application.
     */
    public function shortlisted(): static
    {
        return $this->reviewed('shortlisted');
    }

    /**
     * Create a rejected application.
     */
    public function rejected(): static
    {
        return $this->reviewed('rejected');
    }

    /**
     * Create a hired application.
     */
    public function hired(): static
    {
        return $this->reviewed('hired');
    }

    /**
     * Create an application with attachments.
     */
    public function withAttachments(): static
    {
        return $this->state(fn (array $attributes) => [
            'resume_path' => 'resumes/' . $this->faker->uuid() . '.pdf',
            'portfolio_url' => $this->faker->url(),
            'linkedin_url' => 'https://linkedin.com/in/' . $this->faker->userName(),
        ]);
    }

    /**
     * Create an application with salary expectations.
     */
    public function withSalaryExpectation(float $salary): static
    {
        return $this->state(fn (array $attributes) => [
            'expected_salary' => $salary,
        ]);
    }
}
