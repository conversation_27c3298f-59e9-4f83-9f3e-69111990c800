<?php $__env->startSection('title', $project->title . ' - Project Details - ' . __('common.company_name')); ?>
<?php $__env->startSection('page_title', 'Project Details'); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
                <nav class="flex items-center space-x-2 text-sm text-gray-500 mb-2">
                    <a href="<?php echo e(route('my-projects.index')); ?>" class="hover:text-gray-700">My Projects</a>
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                    <span class="text-gray-900"><?php echo e($project->title); ?></span>
                </nav>
                <h1 class="text-2xl font-bold text-gray-900"><?php echo e($project->title); ?></h1>
                <p class="text-gray-600"><?php echo e($project->description); ?></p>
            </div>
            <div class="flex items-center gap-3">
                <?php if($project->project_url): ?>
                    <a href="<?php echo e($project->project_url); ?>" target="_blank" 
                       class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors duration-200">
                        <svg class="w-4 h-4 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                        </svg>
                        View Live Project
                    </a>
                <?php endif; ?>
                <a href="<?php echo e(route('contact')); ?>" 
                   class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200">
                    <svg class="w-4 h-4 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                    Contact Us
                </a>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Project Status and Progress -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Project Status & Progress</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Status and Priority -->
                    <div>
                        <div class="flex items-center gap-3 mb-4">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                                <?php if($project->status === 'completed'): ?> bg-green-100 text-green-800
                                <?php elseif($project->status === 'in_progress'): ?> bg-blue-100 text-blue-800
                                <?php elseif($project->status === 'review'): ?> bg-purple-100 text-purple-800
                                <?php elseif($project->status === 'planning'): ?> bg-gray-100 text-gray-800
                                <?php elseif($project->status === 'on_hold'): ?> bg-yellow-100 text-yellow-800
                                <?php elseif($project->status === 'cancelled'): ?> bg-red-100 text-red-800
                                <?php else: ?> bg-gray-100 text-gray-800
                                <?php endif; ?>">
                                <?php echo e(ucfirst(str_replace('_', ' ', $project->status))); ?>

                            </span>
                            
                            <?php if($project->priority): ?>
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                                    <?php if($project->priority === 'urgent'): ?> bg-red-100 text-red-800
                                    <?php elseif($project->priority === 'high'): ?> bg-orange-100 text-orange-800
                                    <?php elseif($project->priority === 'medium'): ?> bg-yellow-100 text-yellow-800
                                    <?php elseif($project->priority === 'low'): ?> bg-green-100 text-green-800
                                    <?php else: ?> bg-gray-100 text-gray-800
                                    <?php endif; ?>">
                                    <?php echo e(ucfirst($project->priority)); ?> Priority
                                </span>
                            <?php endif; ?>
                        </div>
                        
                        <?php if($project->service): ?>
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Service Type</label>
                                <span class="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-50 text-blue-700">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                    </svg>
                                    <?php echo e($project->service->title); ?>

                                </span>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Progress Bar -->
                    <?php if($project->estimated_hours && $project->actual_hours): ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Project Progress</label>
                            <div class="mb-2">
                                <div class="flex justify-between text-sm text-gray-600 mb-1">
                                    <span><?php echo e(number_format($project->actual_hours, 1)); ?> / <?php echo e(number_format($project->estimated_hours, 1)); ?> hours</span>
                                    <span><?php echo e($project->progress_percentage); ?>%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-3">
                                    <div class="bg-blue-600 h-3 rounded-full transition-all duration-300" style="width: <?php echo e($project->progress_percentage); ?>%"></div>
                                </div>
                            </div>
                            <?php if($project->progress_percentage >= 100): ?>
                                <p class="text-sm text-green-600 font-medium">Project hours completed!</p>
                            <?php elseif($project->progress_percentage >= 75): ?>
                                <p class="text-sm text-yellow-600 font-medium">Nearing completion</p>
                            <?php else: ?>
                                <p class="text-sm text-gray-600"><?php echo e(100 - $project->progress_percentage); ?>% remaining</p>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Project Description -->
            <?php if($project->content): ?>
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Project Description</h2>
                    <div class="prose max-w-none text-gray-700">
                        <?php echo nl2br(e($project->content)); ?>

                    </div>
                </div>
            <?php endif; ?>

            <!-- Project Gallery -->
            <?php if($project->gallery && count($project->gallery) > 0): ?>
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Project Gallery</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <?php $__currentLoopData = $project->gallery; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="aspect-w-16 aspect-h-9">
                                <img src="<?php echo e($image); ?>" alt="Project image" class="w-full h-48 object-cover rounded-lg">
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Project Actions -->
            <?php if(in_array($project->status, ['planning', 'cancelled', 'on_hold'])): ?>
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Project Actions</h2>
                    <div class="flex items-center justify-between p-4 bg-red-50 rounded-lg">
                        <div>
                            <h3 class="text-sm font-medium text-red-800">Delete Project</h3>
                            <p class="text-sm text-red-600">Permanently remove this project from your account. This action cannot be undone.</p>
                        </div>
                        <form action="<?php echo e(route('my-projects.destroy', $project)); ?>" method="POST" class="inline-block"
                              onsubmit="return confirm('Are you sure you want to delete this project? This action cannot be undone.')">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('DELETE'); ?>
                            <button type="submit" 
                                    class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors duration-200">
                                Delete Project
                            </button>
                        </form>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Project Details -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Project Details</h2>
                
                <div class="space-y-4">
                    <?php if($project->total_amount): ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Project Value</label>
                            <p class="text-2xl font-bold text-gray-900"><?php echo e($project->formatted_total); ?></p>
                            <?php if($project->hourly_rate): ?>
                                <p class="text-sm text-gray-500">R<?php echo e(number_format($project->hourly_rate, 2)); ?> per hour</p>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if($project->start_date): ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                            <p class="text-gray-900"><?php echo e($project->start_date->format('F j, Y')); ?></p>
                        </div>
                    <?php endif; ?>
                    
                    <?php if($project->end_date): ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                <?php if($project->status === 'completed'): ?> Completion Date <?php else: ?> Target Date <?php endif; ?>
                            </label>
                            <p class="text-gray-900"><?php echo e($project->end_date->format('F j, Y')); ?></p>
                            <?php if($project->end_date->isPast() && $project->status !== 'completed'): ?>
                                <p class="text-sm text-red-600 mt-1">Overdue</p>
                            <?php elseif($project->end_date->isToday()): ?>
                                <p class="text-sm text-yellow-600 mt-1">Due today</p>
                            <?php elseif($project->end_date->isFuture()): ?>
                                <p class="text-sm text-gray-500 mt-1"><?php echo e($project->end_date->diffForHumans()); ?></p>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if($project->client_name): ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Client Name</label>
                            <p class="text-gray-900"><?php echo e($project->client_name); ?></p>
                        </div>
                    <?php endif; ?>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Created</label>
                        <p class="text-gray-900"><?php echo e($project->created_at->format('F j, Y')); ?></p>
                        <p class="text-sm text-gray-500"><?php echo e($project->created_at->diffForHumans()); ?></p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Last Updated</label>
                        <p class="text-gray-900"><?php echo e($project->updated_at->format('F j, Y')); ?></p>
                        <p class="text-sm text-gray-500"><?php echo e($project->updated_at->diffForHumans()); ?></p>
                    </div>
                </div>
            </div>

            <!-- Project Timeline -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Project Timeline</h2>
                
                <div class="space-y-4">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <div class="w-3 h-3 bg-blue-600 rounded-full mt-1"></div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-900">Project Created</p>
                            <p class="text-sm text-gray-500"><?php echo e($project->created_at->format('M j, Y \a\t g:i A')); ?></p>
                        </div>
                    </div>
                    
                    <?php if($project->start_date): ?>
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <div class="w-3 h-3 <?php echo e($project->start_date->isPast() ? 'bg-green-600' : 'bg-gray-300'); ?> rounded-full mt-1"></div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-900">Project Started</p>
                                <p class="text-sm text-gray-500"><?php echo e($project->start_date->format('M j, Y')); ?></p>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <?php if($project->end_date): ?>
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <div class="w-3 h-3 <?php echo e($project->status === 'completed' ? 'bg-green-600' : 'bg-gray-300'); ?> rounded-full mt-1"></div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-900">
                                    <?php if($project->status === 'completed'): ?> Project Completed <?php else: ?> Target Completion <?php endif; ?>
                                </p>
                                <p class="text-sm text-gray-500"><?php echo e($project->end_date->format('M j, Y')); ?></p>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
                
                <div class="space-y-3">
                    <a href="<?php echo e(route('my-projects.index')); ?>"
                       class="w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors duration-200 text-center block">
                        ← Back to Projects
                    </a>
                    
                    <a href="<?php echo e(route('contact')); ?>" 
                       class="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200 text-center block">
                        Contact About Project
                    </a>
                    
                    <?php if($project->project_url): ?>
                        <a href="<?php echo e($project->project_url); ?>" target="_blank" 
                           class="w-full bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors duration-200 text-center block">
                            View Live Project
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Success/Error Messages -->
<?php if(session('success')): ?>
    <div class="fixed top-4 right-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg shadow-lg z-50" id="success-message">
        <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <?php echo e(session('success')); ?>

        </div>
    </div>
<?php endif; ?>

<?php if(session('error')): ?>
    <div class="fixed top-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg shadow-lg z-50" id="error-message">
        <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
            <?php echo e(session('error')); ?>

        </div>
    </div>
<?php endif; ?>

<script>
    // Auto-hide success/error messages after 5 seconds
    setTimeout(function() {
        const successMessage = document.getElementById('success-message');
        const errorMessage = document.getElementById('error-message');
        if (successMessage) successMessage.style.display = 'none';
        if (errorMessage) errorMessage.style.display = 'none';
    }, 5000);
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\files\chisolution\resources\views/projects/show.blade.php ENDPATH**/ ?>