<?php $__env->startSection('title', 'Payment Received - ' . $order->order_number); ?>

<?php $__env->startSection('content'); ?>
<div class="greeting">
    Payment Confirmed, <?php echo e($customer_name); ?>!
</div>

<p class="content-text">
    Great news! We've successfully received your payment for order <strong>#<?php echo e($order->order_number); ?></strong>.
</p>

<!-- Payment Confirmation Box -->
<div style="background-color: #f0fff4; border: 1px solid #9ae6b4; border-radius: 8px; padding: 20px; margin: 20px 0;">
    <div style="text-align: center;">
        <div style="font-size: 48px; margin-bottom: 10px;">✅</div>
        <h3 style="margin: 0 0 10px 0; color: #2f855a; font-size: 20px;">Payment Successfully Processed</h3>
        <p style="margin: 0; color: #38a169; font-size: 16px;">
            Amount: <strong><?php echo e($order->formatted_total); ?></strong>
        </p>
    </div>
</div>

<!-- Payment Details -->
<?php if($payment): ?>
<div style="background-color: #f7fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 20px; margin: 20px 0;">
    <h3 style="margin: 0 0 15px 0; color: #2d3748; font-size: 18px;">Payment Details</h3>
    
    <table style="width: 100%; border-collapse: collapse;">
        <tr>
            <td style="padding: 8px 0; color: #4a5568; font-weight: 600;">Payment Method:</td>
            <td style="padding: 8px 0; color: #2d3748; text-align: right; text-transform: capitalize;"><?php echo e($payment->payment_method ?? 'Credit Card'); ?></td>
        </tr>
        <tr>
            <td style="padding: 8px 0; color: #4a5568; font-weight: 600;">Transaction ID:</td>
            <td style="padding: 8px 0; color: #2d3748; text-align: right; font-family: monospace; font-size: 14px;"><?php echo e($payment->transaction_id ?? 'N/A'); ?></td>
        </tr>
        <tr>
            <td style="padding: 8px 0; color: #4a5568; font-weight: 600;">Payment Date:</td>
            <td style="padding: 8px 0; color: #2d3748; text-align: right;"><?php echo e($payment->created_at ? $payment->created_at->format('M d, Y \a\t g:i A') : $order->updated_at->format('M d, Y \a\t g:i A')); ?></td>
        </tr>
        <tr style="border-top: 1px solid #e2e8f0;">
            <td style="padding: 12px 0 8px 0; color: #2d3748; font-weight: 700; font-size: 16px;">Amount Paid:</td>
            <td style="padding: 12px 0 8px 0; color: #2d3748; font-weight: 700; font-size: 16px; text-align: right;"><?php echo e($order->formatted_total); ?></td>
        </tr>
    </table>
</div>
<?php endif; ?>

<!-- Order Summary -->
<div style="background-color: #f7fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 20px; margin: 20px 0;">
    <h3 style="margin: 0 0 15px 0; color: #2d3748; font-size: 18px;">Order Summary</h3>
    
    <table style="width: 100%; border-collapse: collapse;">
        <tr>
            <td style="padding: 8px 0; color: #4a5568; font-weight: 600;">Order Number:</td>
            <td style="padding: 8px 0; color: #2d3748; text-align: right;">#<?php echo e($order->order_number); ?></td>
        </tr>
        <tr>
            <td style="padding: 8px 0; color: #4a5568; font-weight: 600;">Order Date:</td>
            <td style="padding: 8px 0; color: #2d3748; text-align: right;"><?php echo e($order->created_at->format('M d, Y')); ?></td>
        </tr>
        <tr>
            <td style="padding: 8px 0; color: #4a5568; font-weight: 600;">Status:</td>
            <td style="padding: 8px 0; text-align: right;">
                <span style="background-color: #3182ce; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; text-transform: uppercase;">
                    <?php echo e(ucfirst($order->status)); ?>

                </span>
            </td>
        </tr>
    </table>
</div>

<!-- Order Items Summary -->
<h3 style="color: #2d3748; margin: 30px 0 15px 0;">Items in Your Order</h3>

<?php $__currentLoopData = $order->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
<div style="border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px; margin-bottom: 10px; background-color: #ffffff;">
    <table style="width: 100%;">
        <tr>
            <td style="width: 70%;">
                <h4 style="margin: 0 0 5px 0; color: #2d3748; font-size: 16px;"><?php echo e($item->product_name); ?></h4>
                <?php if($item->variant_name): ?>
                <p style="margin: 0 0 5px 0; color: #718096; font-size: 14px;"><?php echo e($item->variant_name); ?></p>
                <?php endif; ?>
                <p style="margin: 0; color: #4a5568; font-size: 14px;">Quantity: <?php echo e($item->quantity); ?></p>
            </td>
            <td style="width: 30%; text-align: right; vertical-align: top;">
                <p style="margin: 0; color: #2d3748; font-weight: 600; font-size: 16px;"><?php echo e($item->formatted_total_price); ?></p>
            </td>
        </tr>
    </table>
</div>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

<!-- Payment Breakdown -->
<div style="background-color: #f7fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 20px; margin: 20px 0;">
    <h3 style="margin: 0 0 15px 0; color: #2d3748; font-size: 18px;">Payment Breakdown</h3>
    
    <table style="width: 100%; border-collapse: collapse;">
        <tr>
            <td style="padding: 5px 0; color: #4a5568;">Subtotal:</td>
            <td style="padding: 5px 0; color: #2d3748; text-align: right;"><?php echo e($order->formatted_subtotal); ?></td>
        </tr>
        
        <?php if($order->discount_amount > 0): ?>
        <tr>
            <td style="padding: 5px 0; color: #38a169;">
                Discount <?php if($order->coupon_code): ?>(<?php echo e($order->coupon_code); ?>)<?php endif; ?>:
            </td>
            <td style="padding: 5px 0; color: #38a169; text-align: right;">-<?php echo e($order->formatted_discount_amount); ?></td>
        </tr>
        <?php endif; ?>
        
        <tr>
            <td style="padding: 5px 0; color: #4a5568;">Shipping:</td>
            <td style="padding: 5px 0; color: #2d3748; text-align: right;"><?php echo e($order->formatted_shipping_amount); ?></td>
        </tr>
        
        <tr>
            <td style="padding: 5px 0; color: #4a5568;">Tax:</td>
            <td style="padding: 5px 0; color: #2d3748; text-align: right;"><?php echo e($order->formatted_tax_amount); ?></td>
        </tr>
        
        <tr style="border-top: 2px solid #2d3748;">
            <td style="padding: 10px 0 5px 0; color: #2d3748; font-weight: 700; font-size: 18px;">Total Paid:</td>
            <td style="padding: 10px 0 5px 0; color: #2d3748; font-weight: 700; font-size: 18px; text-align: right;"><?php echo e($order->formatted_total); ?></td>
        </tr>
    </table>
</div>

<!-- Next Steps -->
<div class="info-box">
    <p><strong>What Happens Next?</strong></p>
    <p>
        Now that your payment has been processed, we'll begin preparing your order for shipment. You can expect:
    </p>
    <ul style="margin: 10px 0; padding-left: 20px; color: #2c5282;">
        <li>Order processing within 1-2 business days</li>
        <li>Shipping notification with tracking information</li>
        <li>Delivery within 3-5 business days (standard shipping)</li>
    </ul>
</div>

<!-- Action Buttons -->
<div class="button-container">
    <?php if($order->user_id): ?>
    <a href="<?php echo e(route('orders.show', $order->uuid)); ?>" class="btn">
        Track Your Order
    </a>
    <?php endif; ?>
    
    <a href="<?php echo e(route('shop.index')); ?>" class="btn btn-secondary" style="margin-left: 10px;">
        Continue Shopping
    </a>
</div>

<!-- Receipt Information -->
<div style="background-color: #fffbeb; border-left: 4px solid #f59e0b; padding: 20px; margin: 20px 0; border-radius: 0 8px 8px 0;">
    <p style="margin: 0; color: #92400e; font-size: 14px;">
        <strong>Receipt:</strong> This email serves as your receipt for this transaction. Please keep it for your records.
    </p>
</div>

<p class="content-text">
    Thank you for your business! If you have any questions about your payment or order, please contact our support team.
</p>

<p class="content-text">
    Best regards,<br>
    The <?php echo e($company_name); ?> Team
</p>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('emails.layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\files\chisolution\resources\views/emails/orders/payment-received.blade.php ENDPATH**/ ?>