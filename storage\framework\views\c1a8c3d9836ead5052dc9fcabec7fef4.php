<?php $__env->startSection('title', 'Payment - ' . __('common.company_name')); ?>
<?php $__env->startSection('meta_description', 'Complete your payment securely'); ?>

<?php $__env->startSection('content'); ?>
<section class="py-20 bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
            <!-- Progress Steps -->
            <div class="mb-8">
                <div class="flex items-center justify-center space-x-4">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-medium">
                            ✓
                        </div>
                        <span class="ml-2 text-sm text-green-600 font-medium">Cart</span>
                    </div>
                    <div class="w-16 h-0.5 bg-green-500"></div>
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-medium">
                            ✓
                        </div>
                        <span class="ml-2 text-sm text-green-600 font-medium">Checkout</span>
                    </div>
                    <div class="w-16 h-0.5 bg-blue-500"></div>
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-medium">
                            3
                        </div>
                        <span class="ml-2 text-sm text-blue-600 font-medium">Payment</span>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Payment Form -->
                <div class="lg:col-span-2">
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-6">Payment Information</h2>

                        <!-- Payment Method Selection -->
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Payment Method</h3>
                            <div class="space-y-3">
                                <label class="flex items-center p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                                    <input type="radio" name="payment_method" value="stripe" checked class="text-blue-600 focus:ring-blue-500">
                                    <div class="ml-3 flex items-center">
                                        <svg class="w-8 h-5 mr-3" viewBox="0 0 40 24" fill="none">
                                            <rect width="40" height="24" rx="4" fill="#635BFF"/>
                                            <path d="M15.27 7.93c0-.69-.57-1.26-1.27-1.26s-1.27.57-1.27 1.26v8.14c0 .69.57 1.26 1.27 1.26s1.27-.57 1.27-1.26V7.93z" fill="white"/>
                                            <path d="M19.73 7.93c0-.69-.57-1.26-1.27-1.26s-1.27.57-1.27 1.26v8.14c0 .69.57 1.26 1.27 1.26s1.27-.57 1.27-1.26V7.93z" fill="white"/>
                                            <path d="M24.19 7.93c0-.69-.57-1.26-1.27-1.26s-1.27.57-1.27 1.26v8.14c0 .69.57 1.26 1.27 1.26s1.27-.57 1.27-1.26V7.93z" fill="white"/>
                                            <path d="M28.65 7.93c0-.69-.57-1.26-1.27-1.26s-1.27.57-1.27 1.26v8.14c0 .69.57 1.26 1.27 1.26s1.27-.57 1.27-1.26V7.93z" fill="white"/>
                                        </svg>
                                        <span class="font-medium text-gray-900">Credit/Debit Card</span>
                                    </div>
                                </label>
                            </div>
                        </div>

                        <!-- Stripe Payment Form -->
                        <div id="stripe-payment-form">
                            <form id="payment-form" class="space-y-6">
                                <?php echo csrf_field(); ?>
                                <input type="hidden" name="order_id" value="<?php echo e($order->uuid); ?>">

                                <!-- Card Element -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        Card Information
                                    </label>
                                    <div id="card-element" class="p-3 border border-gray-300 rounded-lg bg-white">
                                        <!-- Stripe Elements will create form elements here -->
                                    </div>
                                    <div id="card-errors" role="alert" class="mt-2 text-sm text-red-600"></div>
                                </div>

                                <!-- Billing Address -->
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">
                                            Billing Name
                                        </label>
                                        <input type="text" name="billing_name" value="<?php echo e($order->billing_first_name); ?> <?php echo e($order->billing_last_name); ?>" 
                                               class="form-input" required>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">
                                            Email
                                        </label>
                                        <input type="email" name="billing_email" value="<?php echo e($order->email); ?>" 
                                               class="form-input" required>
                                    </div>
                                </div>

                                <!-- Security Notice -->
                                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                                    <div class="flex items-start">
                                        <svg class="w-5 h-5 text-green-600 mt-0.5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                                        </svg>
                                        <div>
                                            <h4 class="text-sm font-medium text-green-800">Secure Payment</h4>
                                            <p class="text-sm text-green-700 mt-1">
                                                Your payment information is encrypted and secure. We use Stripe for payment processing.
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Submit Button -->
                                <button type="submit" id="submit-payment" class="w-full btn-primary">
                                    <span id="button-text">
                                        <svg class="w-5 h-5 mr-2 inline" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                                        </svg>
                                        Pay <?php echo e($order->formatted_total); ?>

                                    </span>
                                    <div id="spinner" class="hidden">
                                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        Processing...
                                    </div>
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Order Summary -->
                <div class="lg:col-span-1">
                    <div class="bg-white rounded-lg shadow-sm p-6 sticky top-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Order Summary</h3>
                        
                        <!-- Order Items -->
                        <div class="space-y-3 mb-6">
                            <?php $__currentLoopData = $order->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="flex items-center space-x-3">
                                <img src="<?php echo e($item->product->primary_image); ?>" alt="<?php echo e($item->product->name); ?>" 
                                     class="w-12 h-12 object-cover rounded">
                                <div class="flex-1 min-w-0">
                                    <h4 class="text-sm font-medium text-gray-900 truncate"><?php echo e($item->product->name); ?></h4>
                                    <?php if($item->productVariant): ?>
                                    <p class="text-xs text-gray-500"><?php echo e($item->productVariant->name); ?></p>
                                    <?php endif; ?>
                                    <p class="text-xs text-gray-500">Qty: <?php echo e($item->quantity); ?></p>
                                </div>
                                <span class="text-sm font-medium text-gray-900"><?php echo e($item->formatted_total); ?></span>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>

                        <!-- Order Totals -->
                        <div class="border-t border-gray-200 pt-4 space-y-3">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Subtotal</span>
                                <span class="text-gray-900"><?php echo e($order->formatted_subtotal); ?></span>
                            </div>

                            <?php if($order->discount_amount > 0): ?>
                            <div class="flex justify-between text-sm text-green-600">
                                <span>Discount <?php if($order->coupon_code): ?>(<?php echo e($order->coupon_code); ?>)<?php endif; ?></span>
                                <span>-<?php echo e($order->formatted_discount_amount); ?></span>
                            </div>
                            <?php endif; ?>

                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Shipping</span>
                                <span class="text-gray-900"><?php echo e($order->formatted_shipping_amount); ?></span>
                            </div>

                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Tax</span>
                                <span class="text-gray-900"><?php echo e($order->formatted_tax_amount); ?></span>
                            </div>

                            <div class="border-t border-gray-200 pt-3">
                                <div class="flex justify-between">
                                    <span class="text-base font-semibold text-gray-900">Total</span>
                                    <span class="text-lg font-bold text-gray-900"><?php echo e($order->formatted_total); ?></span>
                                </div>
                            </div>
                        </div>

                        <!-- Order Info -->
                        <div class="mt-6 pt-6 border-t border-gray-200">
                            <div class="text-sm text-gray-600 space-y-1">
                                <div><strong>Order #:</strong> <?php echo e($order->order_number); ?></div>
                                <div><strong>Email:</strong> <?php echo e($order->email); ?></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php $__env->startPush('scripts'); ?>
<script src="https://js.stripe.com/v3/"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Stripe
    const stripe = Stripe('<?php echo e(config('services.stripe.key')); ?>');
    const elements = stripe.elements();

    // Create card element
    const cardElement = elements.create('card', {
        style: {
            base: {
                fontSize: '16px',
                color: '#424770',
                '::placeholder': {
                    color: '#aab7c4',
                },
            },
        },
    });

    cardElement.mount('#card-element');

    // Handle real-time validation errors from the card Element
    cardElement.on('change', function(event) {
        const displayError = document.getElementById('card-errors');
        if (event.error) {
            displayError.textContent = event.error.message;
        } else {
            displayError.textContent = '';
        }
    });

    // Handle form submission
    const form = document.getElementById('payment-form');
    form.addEventListener('submit', async function(event) {
        event.preventDefault();

        const submitButton = document.getElementById('submit-payment');
        const buttonText = document.getElementById('button-text');
        const spinner = document.getElementById('spinner');

        // Disable submit button and show spinner
        submitButton.disabled = true;
        buttonText.classList.add('hidden');
        spinner.classList.remove('hidden');

        // Create payment method
        const {token, error} = await stripe.createToken(cardElement, {
            name: document.querySelector('input[name="billing_name"]').value,
            email: document.querySelector('input[name="billing_email"]').value,
        });

        if (error) {
            // Show error to customer
            const errorElement = document.getElementById('card-errors');
            errorElement.textContent = error.message;

            // Re-enable submit button
            submitButton.disabled = false;
            buttonText.classList.remove('hidden');
            spinner.classList.add('hidden');
        } else {
            // Submit payment to server
            submitPayment(token.id);
        }
    });

    async function submitPayment(tokenId) {
        try {
            const response = await fetch('<?php echo e(route('checkout.payment.process', $order->uuid)); ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({
                    token: tokenId,
                    order_id: '<?php echo e($order->uuid); ?>'
                })
            });

            const result = await response.json();

            if (result.success) {
                // Redirect to success page
                window.location.href = result.redirect_url || '<?php echo e(route('checkout.success', $order->uuid)); ?>';
            } else {
                // Show error
                const errorElement = document.getElementById('card-errors');
                errorElement.textContent = result.message || 'Payment failed. Please try again.';

                // Re-enable submit button
                const submitButton = document.getElementById('submit-payment');
                const buttonText = document.getElementById('button-text');
                const spinner = document.getElementById('spinner');
                
                submitButton.disabled = false;
                buttonText.classList.remove('hidden');
                spinner.classList.add('hidden');
            }
        } catch (error) {
            console.error('Payment error:', error);
            
            const errorElement = document.getElementById('card-errors');
            errorElement.textContent = 'An error occurred. Please try again.';

            // Re-enable submit button
            const submitButton = document.getElementById('submit-payment');
            const buttonText = document.getElementById('button-text');
            const spinner = document.getElementById('spinner');
            
            submitButton.disabled = false;
            buttonText.classList.remove('hidden');
            spinner.classList.add('hidden');
        }
    }
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\files\chisolution\resources\views/pages/checkout/payment.blade.php ENDPATH**/ ?>