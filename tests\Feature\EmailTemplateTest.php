<?php

namespace Tests\Feature;

use App\Mail\JobApplicationReceived;
use App\Mail\OrderConfirmation;
use App\Mail\OrderStatusUpdate;
use App\Mail\PaymentReceived;
use App\Mail\ProjectApplicationReceived;
use App\Models\JobApplication;
use App\Models\Order;
use App\Models\Payment;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\ProjectApplication;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class EmailTemplateTest extends TestCase
{
    use RefreshDatabase;

    protected $user;

    protected function setUp(): void
    {
        parent::setUp();
        $this->user = User::factory()->create();
    }

    /** @test */
    public function order_confirmation_email_renders_correctly()
    {
        $category = ProductCategory::factory()->create();
        $product = Product::factory()->create([
            'name' => 'Test Product',
            'price' => 100.00,
        ]);
        $product->categories()->attach($category);

        $order = Order::factory()->create([
            'user_id' => $this->user->id,
            'email' => '<EMAIL>',
            'order_number' => 'ORD-12345',
            'subtotal' => 100.00,
            'tax_amount' => 15.00,
            'total_amount' => 115.00,
        ]);

        $order->items()->create([
            'product_id' => $product->id,
            'quantity' => 1,
            'unit_price' => 100.00,
            'total_price' => 100.00,
            'product_snapshot' => [
                'name' => $product->name,
                'price' => $product->price,
                'sku' => $product->sku,
            ],
        ]);

        $mailable = new OrderConfirmation($order);
        $rendered = $mailable->render();

        $this->assertStringContainsString('Thank you for your order', $rendered);
        $this->assertStringContainsString('ORD-12345', $rendered);
        $this->assertStringContainsString('R115.00', $rendered);
    }

    /** @test */
    public function order_confirmation_email_shows_coupon_discount()
    {
        $order = Order::factory()->create([
            'user_id' => $this->user->id,
            'subtotal' => 100.00,
            'discount_amount' => 10.00,
            'coupon_code' => 'SAVE10',
            'total_amount' => 90.00,
        ]);

        $mailable = new OrderConfirmation($order);
        $rendered = $mailable->render();

        $this->assertStringContainsString('SAVE10', $rendered);
        $this->assertStringContainsString('R10.00', $rendered);
        $this->assertStringContainsString('Discount', $rendered);
    }

    /** @test */
    public function payment_received_email_renders_correctly()
    {
        $order = Order::factory()->create([
            'email' => '<EMAIL>',
            'order_number' => 'ORD-67890',
            'total_amount' => 250.00,
            'payment_status' => 'paid',
        ]);

        $payment = Payment::factory()->create([
            'order_id' => $order->id,
            'amount' => 250.00,
            'transaction_id' => 'txn_abc123',
            'payment_method' => 'stripe',
        ]);

        $mailable = new PaymentReceived($order, $payment);
        $rendered = $mailable->render();

        $this->assertStringContainsString('Payment Confirmed', $rendered);
        $this->assertStringContainsString('ORD-67890', $rendered);
        $this->assertStringContainsString('R250.00', $rendered);
        $this->assertStringContainsString('txn_abc123', $rendered);
        $this->assertStringContainsString('Payment Successfully Processed', $rendered);
    }

    /** @test */
    public function order_status_update_email_renders_correctly()
    {
        $order = Order::factory()->create([
            'email' => '<EMAIL>',
            'order_number' => 'ORD-11111',
            'status' => 'shipped',
        ]);

        $mailable = new OrderStatusUpdate($order, 'processing');
        $rendered = $mailable->render();

        $this->assertStringContainsString('Order Update', $rendered);
        $this->assertStringContainsString('ORD-11111', $rendered);
        $this->assertStringContainsString('shipped', $rendered);
    }

    /** @test */
    public function job_application_email_renders_correctly()
    {
        $application = JobApplication::factory()->create([
            'email' => '<EMAIL>',
            'reference_number' => 'JOB-12345',
            'first_name' => 'Alice',
            'last_name' => 'Johnson',
            'cover_letter' => 'I am excited to apply for this position...',
        ]);

        $mailable = new JobApplicationReceived($application);
        $rendered = $mailable->render();

        $this->assertStringContainsString('Thank you for your application, Alice Johnson!', $rendered);
        $this->assertStringContainsString('JOB-12345', $rendered);
        $this->assertStringContainsString('Senior Developer', $rendered);
        $this->assertStringContainsString('I am excited to apply', $rendered);
        $this->assertStringContainsString('5 years of web development', $rendered);
        $this->assertStringContainsString('PHP, Laravel', $rendered);
    }

    /** @test */
    public function project_application_email_renders_correctly()
    {
        $application = ProjectApplication::factory()->create([
            'email' => '<EMAIL>',
            'reference_number' => 'PROJ-54321',
            'full_name' => 'Charlie Brown',
            'project_type' => 'E-commerce Website',
            'description' => 'We need a modern e-commerce platform...',
            'budget_range' => '$10,000 - $20,000',
            'timeline' => '3-4 months',
            'requirements' => 'Mobile responsive, payment integration, inventory management',
        ]);

        $mailable = new ProjectApplicationReceived($application);
        $rendered = $mailable->render();

        $this->assertStringContainsString('Thank you for your project inquiry, Charlie Brown!', $rendered);
        $this->assertStringContainsString('PROJ-54321', $rendered);
        $this->assertStringContainsString('E-commerce Website', $rendered);
        $this->assertStringContainsString('modern e-commerce platform', $rendered);
        $this->assertStringContainsString('$10,000 - $20,000', $rendered);
        $this->assertStringContainsString('3-4 months', $rendered);
        $this->assertStringContainsString('payment integration', $rendered);
    }

    /** @test */
    public function email_templates_include_company_branding()
    {
        $order = Order::factory()->create();
        $mailable = new OrderConfirmation($order);
        $rendered = $mailable->render();

        $this->assertStringContainsString('ChiSolution', $rendered);
        $this->assertStringContainsString('Digital Agency Excellence', $rendered);
        $this->assertStringContainsString(config('app.url'), $rendered);
    }

    /** @test */
    public function email_templates_are_mobile_responsive()
    {
        $order = Order::factory()->create();
        $mailable = new OrderConfirmation($order);
        $rendered = $mailable->render();

        // Check for responsive CSS
        $this->assertStringContainsString('@media only screen and (max-width: 600px)', $rendered);
        $this->assertStringContainsString('width: 100% !important', $rendered);
    }

    /** @test */
    public function email_templates_handle_missing_data_gracefully()
    {
        $order = Order::factory()->create([
            'coupon_code' => null,
        ]);

        $mailable = new OrderConfirmation($order);
        $rendered = $mailable->render();

        // Should render without errors even with missing optional data
        $this->assertStringContainsString('Order Confirmation', $rendered);
        $this->assertStringNotContainsString('null', $rendered);
    }

    /** @test */
    public function email_templates_include_proper_action_buttons()
    {
        $order = Order::factory()->create(['user_id' => $this->user->id]);
        $mailable = new OrderConfirmation($order);
        $rendered = $mailable->render();

        $this->assertStringContainsString('View Order Details', $rendered);
        $this->assertStringContainsString('Continue Shopping', $rendered);
        $this->assertStringContainsString('href=', $rendered);
    }

    /** @test */
    public function email_templates_show_correct_status_colors()
    {
        // Test paid status (green)
        $paidOrder = Order::factory()->create(['payment_status' => 'paid']);
        $mailable = new OrderConfirmation($paidOrder);
        $rendered = $mailable->render();
        $this->assertStringContainsString('#48bb78', $rendered); // Green color

        // Test pending status (orange)
        $pendingOrder = Order::factory()->create(['payment_status' => 'pending']);
        $mailable = new OrderConfirmation($pendingOrder);
        $rendered = $mailable->render();
        $this->assertStringContainsString('#ed8936', $rendered); // Orange color
    }

    /** @test */
    public function email_templates_format_currency_correctly()
    {
        $order = Order::factory()->create([
            'subtotal' => 1234.56,
            'tax_amount' => 185.18,
            'total_amount' => 1419.74,
        ]);

        $mailable = new OrderConfirmation($order);
        $rendered = $mailable->render();

        // Check that currency is formatted properly
        $this->assertStringContainsString('R1,234.56', $rendered);
        $this->assertStringContainsString('R185.18', $rendered);
        $this->assertStringContainsString('R1,419.74', $rendered);
    }
}
